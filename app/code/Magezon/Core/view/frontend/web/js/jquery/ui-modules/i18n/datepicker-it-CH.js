/* Italian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional[ "it-CH" ] = {
	closeText: "Chiudi",
	prevText: "&#x3C;Prec",
	nextText: "Succ&#x3E;",
	currentText: "Oggi",
	monthNames: [ "<PERSON>nai<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "April<PERSON>", "Ma<PERSON>", "Giugno",
		"Luglio", "Agosto", "Settembre", "Ottobre", "Novembre", "Dicembre" ],
	monthNamesShort: [ "Gen", "Feb", "<PERSON>", "Apr", "Mag", "<PERSON><PERSON>",
		"Lug", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Dic" ],
	dayNames: [ "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mercoledì", "Giovedì", "Venerdì", "Sabato" ],
	dayNamesShort: [ "Dom", "Lun", "Mar", "Mer", "Gio", "Ven", "Sab" ],
	dayNamesMin: [ "Do", "Lu", "Ma", "Me", "Gi", "Ve", "Sa" ],
	weekHeader: "Sm",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional[ "it-CH" ] );

return datepicker.regional[ "it-CH" ];

} );
