<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationProducts\Model;

use Exception;
use MadHat\DbLogger\Logger\DbLoggerSaver;
use MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider;
use MadHat\SiteIntegrationProducts\Api\Data\ProductDataInterface;
use MadHat\SiteIntegrationProducts\Api\Data\VariantAttributeDataInterface;
use MadHat\SiteIntegrationProducts\Api\DoptionsRepositoryInterface;
use MadHat\SiteIntegrationProducts\Helper\Data as MadHatProductHelper;
use MadHat\SiteIntegrationProducts\Logger\Logger as MadhatProductLogger;
use Magento\Catalog\Api\CategoryListInterface;
use Magento\Catalog\Api\Data\ProductAttributeInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\Data\ProductLinkInterfaceFactory;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\CategoryLinkRepository;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Catalog\Model\Product\Visibility;
use Magento\Catalog\Model\ProductFactory;
use Magento\Catalog\Model\ResourceModel\Eav\AttributeFactory;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory as ProductCollectionFactory;
use Magento\CatalogImportExport\Model\Import\Proxy\Product\ResourceModelFactory;
use Magento\CatalogInventory\Model\Stock;
use Magento\ConfigurableProduct\Api\LinkManagementInterface;
use Magento\ConfigurableProduct\Helper\Product\Options\Factory;
use Magento\ConfigurableProduct\Model\LinkManagement;
use Magento\Eav\Api\AttributeOptionManagementInterface;
use Magento\Eav\Api\AttributeRepositoryInterface;
use Magento\Eav\Api\AttributeSetRepositoryInterface;
use Magento\Eav\Api\Data\AttributeOptionInterfaceFactory;
use Magento\Eav\Model\ResourceModel\Entity\Attribute\Set\CollectionFactory as AttributeSetCollectionFactory;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Cache\Manager as CacheManager;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\StateException;
use Magento\Store\Model\App\Emulation;
use Magento\Store\Model\Store;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Swatches\Helper\Data as SwatchHelper;

/**
 * Logic add/update products for Magento 2
 */
class ProductProcessor
{
    /**
     * Regular(Simple) Product Value from SITE RMQ data
     */
    public const REGULAR_PRODUCT_VALUE = 0;

    /**
     * Base(Configurable) Product Value from SITE RMQ data
     */
    public const BASE_PRODUCT_VALUE = 1;

    /**
     * Variant(Simple/Associated) Product Value from SITE RMQ data
     */
    public const VARIANT_PRODUCT_VALUE = 2;

    /**
     * Default price value for product
     */
    public const PRODUCT_DEFAULT_PRICE = 0;

    /**
     * Default quantity value for product
     */
    public const PRODUCT_DEFAULT_QTY = 0;

    /**
     * @var array
     */
    protected array $attributeOptions = [];

    /**
     * @var int|null
     */
    private ?int $totalProducts;

    /**
     * @var int|null
     */
    private ?int $savedProducts;

    /**
     * @var int|null
     */
    private ?int $failedProducts;

    /**
     * @var array
     */
    private array $productImportErrors;

    /**
     * @var array
     */
    protected array $configurableArray = [];

    /**
     * @var array
     */
    protected array $productRelationsArray = [];

    /**
     * @var array
     */
    protected array $primaryVariantArray = [];

    /**
     * @var array
     */
    protected array $attributeSetMapping = [];

    /**
     * @var array
     */
    private array $productTaxMapping = [];

    /**
     * Variant Attributes mapping with Attribute Code
     *
     * @var array|array[]
     */
    protected array $variantAttributesMapping = [
        'VariantColor' => [
            'code' => 'madhat_color'
        ],
        'VariantWeight' => [
            'code' => 'madhat_weight'
        ],
        'VariantFilamentSize' => [
            'code' => 'madhat_filament_size'
        ],
        'VariantVoltage' => [
            'code' => 'madhat_voltage'
        ],
        'VariantE3DTemperatureSensor' => [
            'code' => 'madhat_e3d_temperature_sensor'
        ],
        'VariantE3DHotsideKit' => [
            'code' => 'madhat_e3d_hotside_kit'
        ],
        'VariantNozzleDiameter' => [
            'code' => 'madhat_nozzle_diameter'
        ],
        'VariantXToolBundle' => [
            'code' => 'madhat_x_tool_bundle'
        ],
        'VariantXToolRA' => [
            'code' => 'madhat_x_tool_ra'
        ]
    ];

    /**
     * @var DbLoggerSaver
     */
    protected DbLoggerSaver $dbLoggerSaver;

    /**
     * @var DoptionsRepositoryInterface
     */
    protected DoptionsRepositoryInterface $doptionsRepository;

    /**
     * @var MadHatProductHelper
     */
    protected MadHatProductHelper $madHatProductHelper;

    /**
     * @var MadhatProductLogger
     */
    protected MadhatProductLogger $logger;

    /**
     * @var CategoryListInterface
     */
    protected CategoryListInterface $categoryList;

    /**
     * @var ProductLinkInterfaceFactory
     */
    protected ProductLinkInterfaceFactory $productLinkFactory;

    /**
     * @var ProductRepositoryInterface
     */
    protected ProductRepositoryInterface $productRepository;

    /**
     * @var CategoryLinkRepository
     */
    protected CategoryLinkRepository $categoryLinkRepository;

    /**
     * @var ProductFactory
     */
    protected ProductFactory $productFactory;

    /**
     * @var AttributeFactory
     */
    protected AttributeFactory $attributeFactory;

    /**
     * @var ResourceModelFactory
     */
    protected ResourceModelFactory $resourceModelFactory;

    /**
     * @var Factory
     */
    protected Factory $configurableOptionsFactory;

    /**
     * @var LinkManagement
     */
    protected LinkManagement $configurableLinkManagement;

    /**
     * @var AttributeOptionManagementInterface
     */
    protected AttributeOptionManagementInterface $attributeOptionManagement;

    /**
     * @var AttributeRepositoryInterface
     */
    protected AttributeRepositoryInterface $attributeRepository;

    /**
     * @var AttributeSetRepositoryInterface
     */
    protected AttributeSetRepositoryInterface $attributeSetRepository;

    /**
     * @var AttributeOptionInterfaceFactory
     */
    protected AttributeOptionInterfaceFactory $attributeOptionInterfaceFactory;

    /**
     * @var AttributeSetCollectionFactory
     */
    protected AttributeSetCollectionFactory $attributeSetCollectionFactory;

    /**
     * @var EavSetupFactory
     */
    protected EavSetupFactory $eavSetupFactory;

    /**
     * @var SearchCriteriaBuilder
     */
    protected SearchCriteriaBuilder $searchCriteriaBuilder;

    /**
     * @var CacheManager
     */
    protected CacheManager $cacheManager;

    /**
     * @var ScopeConfigInterface
     *
     */
    protected ScopeConfigInterface $scopeConfig;

    /**
     * @var AdapterInterface
     *
     */
    protected AdapterInterface $connection;

    /**
     * @var Emulation
     *
     */
    protected Emulation $emulation;

    /**
     * @var StoreManagerInterface
     */
    protected StoreManagerInterface $storeManager;

    /**
     * @var SwatchHelper
     */
    protected SwatchHelper $swatchHelper;

    /**
     * @var ProductCollectionFactory
     */
    protected ProductCollectionFactory $productCollectionFactory;

    /**
     * @var LinkManagementInterface
     */
    protected LinkManagementInterface $linkManagement;

    /**
     * @param DbLoggerSaver $dbLoggerSaver
     * @param MadHatProductHelper $madHatProductHelper
     * @param MadhatProductLogger $logger
     * @param CategoryListInterface $categoryList
     * @param ProductLinkInterfaceFactory $productLinkFactory
     * @param ProductRepositoryInterface $productRepository
     * @param CategoryLinkRepository $categoryLinkRepository
     * @param ProductFactory $productFactory
     * @param AttributeFactory $attributeFactory
     * @param ResourceModelFactory $resourceModelFactory
     * @param Factory $configurableOptionsFactory
     * @param LinkManagement $configurableLinkManagement
     * @param AttributeOptionManagementInterface $attributeOptionManagement
     * @param AttributeRepositoryInterface $attributeRepository
     * @param AttributeSetRepositoryInterface $attributeSetRepository
     * @param AttributeOptionInterfaceFactory $attributeOptionInterfaceFactory
     * @param AttributeSetCollectionFactory $attributeSetCollectionFactory
     * @param EavSetupFactory $eavSetupFactory
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param CacheManager $cacheManager
     * @param ScopeConfigInterface $scopeConfig
     * @param ResourceConnection $resource
     * @param Emulation $emulation
     * @param StoreManagerInterface $storeManager
     * @param SwatchHelper $swatchHelper
     * @param ProductCollectionFactory $productCollectionFactory
     * @param LinkManagementInterface $linkManagement
     */
    public function __construct(
        DbLoggerSaver $dbLoggerSaver,
        MadHatProductHelper $madHatProductHelper,
        MadhatProductLogger $logger,
        CategoryListInterface $categoryList,
        ProductLinkInterfaceFactory $productLinkFactory,
        ProductRepositoryInterface $productRepository,
        CategoryLinkRepository $categoryLinkRepository,
        ProductFactory $productFactory,
        AttributeFactory $attributeFactory,
        ResourceModelFactory $resourceModelFactory,
        Factory $configurableOptionsFactory,
        LinkManagement $configurableLinkManagement,
        AttributeOptionManagementInterface $attributeOptionManagement,
        AttributeRepositoryInterface $attributeRepository,
        AttributeSetRepositoryInterface $attributeSetRepository,
        AttributeOptionInterfaceFactory $attributeOptionInterfaceFactory,
        AttributeSetCollectionFactory $attributeSetCollectionFactory,
        EavSetupFactory $eavSetupFactory,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        CacheManager $cacheManager,
        ScopeConfigInterface $scopeConfig,
        ResourceConnection $resource,
        Emulation $emulation,
        StoreManagerInterface $storeManager,
        SwatchHelper $swatchHelper,
        ProductCollectionFactory $productCollectionFactory,
        LinkManagementInterface $linkManagement
    ) {
        $this->dbLoggerSaver = $dbLoggerSaver;
        $this->madHatProductHelper = $madHatProductHelper;
        $this->logger = $logger;
        $this->categoryList = $categoryList;
        $this->productLinkFactory = $productLinkFactory;
        $this->productRepository = $productRepository;
        $this->categoryLinkRepository = $categoryLinkRepository;
        $this->productFactory = $productFactory;
        $this->attributeFactory = $attributeFactory;
        $this->resourceModelFactory = $resourceModelFactory;
        $this->configurableOptionsFactory = $configurableOptionsFactory;
        $this->configurableLinkManagement = $configurableLinkManagement;
        $this->attributeOptionManagement = $attributeOptionManagement;
        $this->attributeRepository = $attributeRepository;
        $this->attributeSetRepository = $attributeSetRepository;
        $this->attributeOptionInterfaceFactory = $attributeOptionInterfaceFactory;
        $this->attributeSetCollectionFactory = $attributeSetCollectionFactory;
        $this->eavSetupFactory = $eavSetupFactory;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->cacheManager = $cacheManager;
        $this->scopeConfig = $scopeConfig;
        $this->connection = $resource->getConnection();
        $this->emulation = $emulation;
        $this->storeManager = $storeManager;
        $this->swatchHelper = $swatchHelper;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->linkManagement = $linkManagement;
    }

    /**
     * Process message to import products
     *
     * @param array $message
     * @param int $websiteId
     * @return void
     * @throws LocalizedException
     */
    public function processMessage(array $message, int $websiteId): void
    {
        try {
            $this->totalProducts = count($message);
            $this->savedProducts = 0;
            $this->failedProducts = 0;
            $this->productImportErrors = [];

            $this->configurableArray = [];
            $this->primaryVariantArray = [];
            $this->attributeOptions = [];

            if (empty($this->attributeSetMapping)) {
                $attributeSetMapping = $this->madHatProductHelper->getAttributeSetMapping();
                if (empty($attributeSetMapping)) {
                    $this->productImportErrors[] = [
                        'ErrorMessage' => 'Product Attribute Set Mapping missing.',
                    ];
                    return;
                }
                $this->attributeSetMapping = $attributeSetMapping;
            }

            $this->productTaxMapping = $this->madHatProductHelper->getProductTaxCodeMapping();

            /** @var ProductDataInterface $item */
            foreach ($message as $item) {
                $this->logger->info(__(
                    '%1 => %2 All Fine Processing item SKU: %3, DATA: %4',
                    __CLASS__,
                    __FUNCTION__,
                    $item->getProductNo(),
                    print_r($item, true)
                ));
                try {
                    if ($this->validateProductData($item)) {
                        if ($item->getProductClassValue() == self::BASE_PRODUCT_VALUE) {
                            $product = $this->createConfigurableProduct($item, $websiteId);
                            $this->unsetCategoryFromProduct($product);
                        } else {
                            $product = $this->createSimpleProduct($item, $websiteId);
                            $this->unsetCategoryFromProduct($product);
                            $this->mapProductToCategories($product, $item);
                        }
                        if ($product) {
                            $this->storeProductRelations($item);
                            $this->savedProducts++;
                        } else {
                            $this->failedProducts++;
                        }
                    } else {
                        $this->failedProducts++;
                    }
                } catch (Exception $e) {
                    $this->failedProducts++;
                    $this->logger->error(
                        __(
                            'Product SKU : %1 => Exception : %2',
                            $item->getProductNo(),
                            $e->getMessage()
                        )
                    );
                }
            }
            $this->assignSimpleProductsToConfigurableProduct();
            $this->assignPrimaryVariantToConfigurableProduct();
            $this->setProductRelationsForProducts();
        } catch (Exception $e) {
            $this->logger->error(
                __(
                    '%1::%2() => Exception : %3',
                    __CLASS__,
                    __FUNCTION__,
                    $e->getMessage()
                )
            );
        }
        $this->addDbLoggerRecord($websiteId);
    }

    /**
     * Create Simple Product
     *
     * @param ProductDataInterface $productData
     * @param int $websiteId
     * @return bool|ProductInterface
     *
     * @throws CouldNotSaveException
     * @throws InputException
     * @throws LocalizedException
     * @throws StateException
     */
    public function createSimpleProduct(ProductDataInterface $productData, int $websiteId): bool|ProductInterface
    {
        try {
            $product = $this->productRepository->get($productData->getProductNo(), false, Store::DEFAULT_STORE_ID, true);
        } catch (NoSuchEntityException $e) {
            $product = $this->productFactory->create();
            $product->setStatus(Status::STATUS_DISABLED)
                ->setPrice(self::PRODUCT_DEFAULT_PRICE);
        }

        $productWebsites = $product->getWebsiteIds();
        $productWebsites[] = $websiteId;

        if ($productData->getBaseProductNo()) {
            $this->configurableArray[$productData->getBaseProductNo()]['variant_products'][] = $productData->getProductNo();

            $isPrimary = $productData->getIsPrimaryVariant();
            if ($isPrimary) {
                $this->primaryVariantArray[$productData->getBaseProductNo()] = $productData->getProductNo();
            }
        }

        $attributeSetName = $this->madHatProductHelper->getAttributeSetName($productData, $this->attributeSetMapping);
        $attributeSetId = $this->madHatProductHelper->getAttributeSetId($attributeSetName);
        $urlKey = $this->madHatProductHelper->generateUrlKey($productData);

        $productName = $this->getProductName($productData, $product);

        $product
            ->setTypeId('simple')
            ->setVisibility(Visibility::VISIBILITY_BOTH)
            ->setAttributeSetId($attributeSetId)
            ->setName($productName)
            ->setSku($productData->getProductNo())
            ->setWeight($this->madHatProductHelper->getWeightValue($productData->getWeight()))
            ->setUrlKey($urlKey)
            ->setCost($productData->getCostPrice())
            ->setStoreId(Store::DEFAULT_STORE_ID);

        $taxClassId = 0;
        if (!empty($this->productTaxMapping)) {
            $taxClassId = $this->productTaxMapping[$productData->getTaxClassValue()];
        }
        $product->setTaxClassId($taxClassId);

        $product->setData('madhat_ean', trim($productData->getEAN()));
        $product->setData('madhat_manufacturer_number', trim($productData->getManufacturerProductNo()));
        $product->setData('madhat_length', $productData->getLength());
        $product->setData('madhat_width', $productData->getWidth());
        $product->setData('madhat_height', $productData->getHeight());

        $product->setWebsiteIds($productWebsites);

        /**
         * Product Attributes processing
         */
        if (!empty($productData->getProductAttributes())) {
            $product = $this->processProductAttributes($productData, $product);
        }

        /**
         * Variant Attributes processing
         */
        if (!empty($productData->getVariantAttributes())) {
            $product = $this->processVariantAttributes($productData, $product);
        }

        /**
         * Hide child products from frontend (in case of Bundle Product)
         */
        if (!empty($productData->getBundledProducts())) {
            $this->hideProductsFromFrontend($productData->getBundledProducts());
        }

        return $this->productRepository->save($product);
    }

    /**
     * Create Configurable Product
     *
     * @param ProductDataInterface $productData
     * @param int $websiteId
     * @return bool|ProductInterface
     * @throws CouldNotSaveException
     * @throws InputException
     * @throws LocalizedException
     * @throws StateException
     */
    public function createConfigurableProduct(ProductDataInterface $productData, int $websiteId): bool|ProductInterface
    {
        try {
            $product = $this->productRepository->get($productData->getProductNo(), false, Store::DEFAULT_STORE_ID, true);

            $primaryVariant = $product->getData('simple_preselect');
            $childProducts = $this->linkManagement->getChildren($product->getSku());
            if ($primaryVariant && !in_array($primaryVariant, $childProducts)) {
                $product->setData('simple_preselect', '');
            }
        } catch (NoSuchEntityException $e) {
            $product = $this->productFactory->create();
        }

        $productWebsites = $product->getWebsiteIds();
        $productWebsites[] = $websiteId;

        if (isset($this->configurableArray[$productData->getProductNo()])) {
            $this->configurableArray[$productData->getProductNo()] = array_merge(
                $productData->getVariantProducts(),
                $this->configurableArray[$productData->getProductNo()]
            );
        } else {
            $this->configurableArray[$productData->getProductNo()]['variant_products'] = $productData->getVariantProducts();
        }

        $attributeSetName = $this->madHatProductHelper->getAttributeSetName($productData, $this->attributeSetMapping);
        if (empty($attributeSetName)) {
            $this->productImportErrors[] = [
                'ProductNo' => $productData->getProductNo(),
                'ErrorMessage' => 'Incorrect value for ProductType or ProductSubType.',
            ];
            return false;
        }
        $attributeSetId = $this->madHatProductHelper->getAttributeSetId($attributeSetName);
        $urlKey = $this->madHatProductHelper->generateUrlKey($productData);

        $productName = $this->getProductName($productData, $product);

        $product
            ->setTypeId('configurable')
            ->setAttributeSetId($attributeSetId)
            ->setStatus(Status::STATUS_ENABLED)
            ->setVisibility(Visibility::VISIBILITY_IN_CATALOG)
            ->setName($productName)
            ->setSku($productData->getProductNo())
            ->setPrice(self::PRODUCT_DEFAULT_PRICE)
            ->setUrlKey($urlKey)
            ->setWeight($this->madHatProductHelper->getWeightValue($productData->getWeight()))
            ->setStockData([
                'use_config_manage_stock'=> 0,
                'manage_stock' => 0,
                'qty' => self::PRODUCT_DEFAULT_QTY,
                'is_in_stock' => Stock::STOCK_IN_STOCK
            ])
            ->setStoreId(Store::DEFAULT_STORE_ID);

        $taxClassId = 0;
        if (!empty($this->productTaxMapping)) {
            $taxClassId = $this->productTaxMapping[$productData->getTaxClassValue()];
        }
        $product->setTaxClassId($taxClassId);

        $product->setWebsiteIds($productWebsites);
        $product->setCategoryIds([]);

        $product->setData('madhat_ean', trim($productData->getEAN()));
        $product->setData('madhat_manufacturer_number', trim($productData->getManufacturerProductNo()));

        /**
         * Dynamic options processing
         */
        // Brand
        if (!empty($productData->getProductAttributes())) {
            $product = $this->processProductAttributes($productData, $product);
        }

        return $this->productRepository->save($product);
    }

    /**
     * Assign Associate Products to Configurable|Parent Product
     *
     * @return void
     * @throws NoSuchEntityException|LocalizedException
     */
    public function assignSimpleProductsToConfigurableProduct(): void
    {
        try {
            $configurableProducts = $this->configurableArray;
            foreach ($configurableProducts as $configurableProductSku => $configurableProductData) {
                if (!isset($configurableProductData['variant_products'])) {
                    continue;
                }
                $simpleProductSkus = $configurableProductData['variant_products'];
                if (isset($configurableProductData['variant_attributes'])) {
                    $configurableAttributeCodes = $configurableProductData['variant_attributes'];
                } else {
                    $configurableProduct = $this->productRepository->get($configurableProductSku, false, Store::DEFAULT_STORE_ID, true);
                    $attributeSetId = $configurableProduct->getAttributeSetId();
                    $attributeSetName = $this->attributeSetRepository->get($attributeSetId)->getAttributeSetName();
                    $variantAttributes = $this->getVariantAttributes($attributeSetName);
                    $configurableAttributeCodes = [];
                    foreach ($variantAttributes as $variantAttribute) {
                        $configurableAttributeCodes[] = $this->variantAttributesMapping[$variantAttribute]['code'];
                    }
                }

                $configurableProduct = $this->productRepository->get($configurableProductSku, false, Store::DEFAULT_STORE_ID, true);

                $configurableAttributesData = [];
                $position = 0;
                foreach ($configurableAttributeCodes as $attributeCode) {
                    $attributeValues = [];
                    $attribute = $this->attributeRepository->get('catalog_product', $attributeCode);
                    $options = $attribute->getOptions();
                    $option = array_shift($options);
                    $attributeValues[] = [
                        'attribute_id' => $attribute->getId(),
                        'value_index' => $option->getValue(),
                    ];

                    $configurableAttributesData[] = [
                        'attribute_id' => $attribute->getId(),
                        'code' => $attribute->getAttributeCode(),
                        'label' => $attribute->getStoreLabel(),
                        'position' => $position,
                        'values' => $attributeValues,
                    ];
                    $position++;
                }

                $configurableOptions = $this->configurableOptionsFactory->create($configurableAttributesData);
                $extensionAttributes = $configurableProduct->getExtensionAttributes();
                $extensionAttributes->setConfigurableProductOptions($configurableOptions);
                $configurableProduct->setExtensionAttributes($extensionAttributes);
                $configurableProduct->setTypeId('configurable');
                $configurableProduct->setStoreId(Store::DEFAULT_STORE_ID);
                $this->productRepository->save($configurableProduct);

                $linkedAssociatedProducts = $configurableProduct->getTypeInstance()->getUsedProducts(
                    $configurableProduct
                );
                $linkedAssociatedProductSkus = [];
                foreach ($linkedAssociatedProducts as $linkedAssociatedProduct) {
                    $linkedAssociatedProductSkus[] = $linkedAssociatedProduct->getSku();
                }

                $simpleProductSkus = array_unique($simpleProductSkus);
                foreach ($simpleProductSkus as $simpleProductSku) {
                    try {
                        if (!in_array($simpleProductSku, $linkedAssociatedProductSkus)) {
                            if ($this->productRepository->get($simpleProductSku, false, Store::DEFAULT_STORE_ID, true)) {
                                // to skip cached configurable product data.
                                // Force reload of configurable product is done
                                // to get extension attribute for configurable attributes from fresh object.
                                $configurableProduct = $this->productRepository->get(
                                    $configurableProductSku,
                                    true,
                                    Store::DEFAULT_STORE_ID,
                                    true
                                );
                                $this->configurableLinkManagement->addChild($configurableProductSku, $simpleProductSku);
                            }
                        }
                    } catch (StateException $e) {
                        $this->logger->error(
                            __(
                                'Parent SKU : [%1], Child SKU : [%2] | Error : %3',
                                $configurableProductSku,
                                $simpleProductSku,
                                $e->getMessage()
                            )
                        );
                    } catch (Exception $e) {
                        $this->logger->error(
                            __(
                                'Parent SKU : [%1], Child SKU : [%2] | Error : %3',
                                $configurableProductSku,
                                $simpleProductSku,
                                $e->getMessage()
                            )
                        );
                        $this->productImportErrors[] = [
                            'ProductNo' => $configurableProductSku,
                            'MagentoErrorMessage' => $e->getMessage()
                        ];
                    }
                }
            }

        } catch (Exception $e) {
            $this->logger->error(
                __(
                    '%1 => %2[%3] => %4',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $e->getMessage()
                )
            );
            $this->productImportErrors[] = [
                'ProductNo' => $configurableProductSku,
                'MagentoErrorMessage' => $e->getMessage()
            ];
        }
    }

    /**
     * Save DOptions Values
     *
     * @param ProductInterface|Product $product
     * @param ProductDataInterface $productData
     * @param string $variantAttributeCode
     * @return ProductInterface|Product
     */
    public function setProductAttribute(
        ProductInterface|Product $product,
        ProductDataInterface     $productData,
        string                   $variantAttributeCode
    ): ProductInterface|Product {
        try {
            $productAttributes = $productData->getProductAttributes();
            foreach ($productAttributes as $productAttribute) {
                if ($productAttribute->getName() == $variantAttributeCode) {
                    $productAttributeName = $productAttribute->getName();
                    $productAttributeValue = $productAttribute->getValue();
                    $productAttributeLabel = $productAttribute->getLabel();
                    break;
                }
            }

            if ($productAttributeValue == 0) {
                $this->logger->error(
                    __(
                        'Product SKU : %1, Attribute Mapping not found for %2',
                        $productData->getProductNo(),
                        $variantAttributeCode
                    )
                );
            }

            if ($productAttributeName == 'Color') {
                $attributeCode = 'madhat_base_color';
            }
            $attributeOptionId = $this->getAttributeOptionIdByLabel($attributeCode, $productAttributeLabel);
            $product->setData($attributeCode, $attributeOptionId);
        } catch (\Exception $e) {
            $this->logger->error(
                __(
                    'Product SKU : %1, Variant Attribute : %2 => Exception : %3',
                    $productData->getProductNo(),
                    $variantAttributeCode,
                    $e->getMessage()
                )
            );
        }

        return $product;
    }

    /**
     * Save Variant Attribute Values in Doptions table and EAV attribute options.
     *
     * @param ProductInterface|Product $product
     * @param ProductDataInterface $productData
     * @param VariantAttributeDataInterface $variantAttribute
     * @return ProductInterface|Product
     * @throws InputException
     * @throws LocalizedException
     * @throws StateException
     * @throws Exception
     */
    public function setVariantAttribute(
        ProductInterface|Product      $product,
        ProductDataInterface          $productData,
        VariantAttributeDataInterface $variantAttribute
    ): ProductInterface|Product {
        $variantAttributeName = $variantAttribute->getName();
        $variantAttributeValue = $variantAttribute->getValue();
        $variantAttributeLabel = $variantAttribute->getLabel();

        $attributeCode = $this->variantAttributesMapping[$variantAttributeName]['code'];
        $attributeModel = $this->attributeFactory->create()->loadByCode(Product::ENTITY, $attributeCode);
        if ($this->swatchHelper->isTextSwatch($attributeModel)) {
            if ($variantAttributeName == 'VariantFilamentSize') {
                $variantAttributeLabel = $this->madHatProductHelper->fixVariantFilamentSize($variantAttributeValue);
            }
            if ($variantAttributeName == 'VariantWeight') {
                $variantAttributeLabel = $this->madHatProductHelper->fixVariantWeight($variantAttributeValue);
            }
            if ($variantAttributeName == 'VariantNozzleDiameter') {
                $variantAttributeLabel = $this->madHatProductHelper->fixVariantNozzleDiameter($variantAttributeValue);
            }
            if ($variantAttributeName == 'VariantVoltage') {
                $variantAttributeLabel = $this->madHatProductHelper->fixVariantVoltage($variantAttributeValue);
            }
        }
        try {
            if ($variantAttributeLabel == '' || $variantAttributeLabel == '0') {
                $variantAttributeLabel = 'N/A';
            } else {
                $this->configurableArray[$productData->getBaseProductNo()]['variant_attributes'][] = $attributeCode;
            }
            $attributeOptionId = $this->getAttributeOptionIdByLabel($attributeCode, $variantAttributeLabel);
        } catch (\Exception $e) {
            if ($this->swatchHelper->isVisualSwatch($attributeModel)) {
                $attributeOptionId = $this->createAttributeOptionIdByLabelForVisualSwatch(
                    $attributeCode,
                    $variantAttributeLabel
                );
            } elseif ($this->swatchHelper->isTextSwatch($attributeModel)) {
                $attributeOptionId = $this->createAttributeOptionIdByLabelForTextSwatch(
                    $attributeCode,
                    $variantAttributeLabel
                );
            } else {
                $attributeOptionId = $this->createAttributeOptionIdByLabelForDropDown(
                    $attributeCode,
                    $variantAttributeLabel
                );
            }
        }

        $product->setData($attributeCode, $attributeOptionId);

        // Add 'madhat_base_color' attribute value for product only, with 'madhat_color' value.
        if ($variantAttributeName == 'VariantColor') {
            $product = $this->setProductAttribute($product, $productData, 'Color');
        }

        return $product;
    }

    /**
     * Get Product Attribute
     *
     * @param ProductDataInterface $productData
     * @param string $name
     * @return string
     */
    public function getProductAttributes(ProductDataInterface $productData, string $name): string
    {
        foreach ($productData->getProductAttributes() as $productAttribute) {
            if ($productAttribute->getName() == $name) {
                if ($name == 'Color') {
                    return (string)$productAttribute->getLabel();
                }
                return (string)$productAttribute->getValue();
            }
        }
        return '';
    }

    /**
     * Get Product Attribute Values from ProductData
     *
     * @param ProductDataInterface $productData
     * @param string $name
     * @param string $type
     * @return array
     */
    public function getProductAttrOption(ProductDataInterface $productData, string $name, string $type): array
    {
        if ($type == "VariantAttribute") {
            $productAttributesData = $productData->getVariantAttributes();
        } else {
            $productAttributesData = $productData->getProductAttributes();
        }
        foreach ($productAttributesData as $productAttribute) {
            if ($productAttribute->getName() == $name) {
                return [
                    'Name' => $productAttribute->getName(),
                    'Description' => $productAttribute->getDescription(),
                    'Value' => $productAttribute->getValue(),
                    'Label' => $productAttribute->getLabel(),
                ];
            }
        }
        return [];
    }

    /**
     * Get array of Associated Product Id's from Product's SKU.
     *
     * @param array $simpleProductSkus
     * @return array
     */
    public function getAssociatedProductIds(array $simpleProductSkus): array
    {
        $associatedProductIds = [];

        foreach ($simpleProductSkus as $simpleProductSku) {
            try {
                $associatedProductIds[] = $this->productRepository->get($simpleProductSku, false, Store::DEFAULT_STORE_ID, true)->getId();
            } catch (Exception $e) {
                $this->logger->error(__(
                    '%1 => %2 Error : %3',
                    __CLASS__,
                    __FUNCTION__,
                    __('Product SKU : "%1" doesn\'t exist.', $simpleProductSku)
                ));
            }
        }

        return $associatedProductIds;
    }

    /**
     * Map Product with Category on the basis of External|Visma|SITE Category ID
     *
     * @param ProductInterface $product
     * @param ProductDataInterface $productData
     * @return bool
     * @throws NoSuchEntityException
     */
    protected function mapProductToCategories(ProductInterface $product, ProductDataInterface $productData): bool
    {
        $categoryIds = [
            $this->getCategoryIdByExternalCategoryId($productData->getProductType()),
            $this->getCategoryIdByExternalCategoryId($productData->getProductSubType()),
            $this->getCategoryIdByExternalCategoryId($productData->getCategoryLevel2())
        ];

        $product = $this->productFactory->create()->load($product->getId());

        $assignedCategories = $product->getCategoryIds();

        if ($categoryIds) {
            $assignedCategories = array_merge($assignedCategories, $categoryIds);
        }
        $assignedCategories = array_values(array_filter($assignedCategories));

        //Add & Remove Category as per the requirement based on Final categories ID $finalCatIds
        // Set Store Id as 1 intentionally for Fix All Store-view Data to German/Default bug fix
        //$this->childCategories->assignProductToCategories($product->getSku(), $finalCatIds, $this->productLogger);
        $this->categoriesCache = [];
        foreach ($assignedCategories as $catId) {
            if ($catId) {
                $this->categoriesCache[$product->getSku()][$catId] = true;
            }
        }

        $this->_saveProductCategories($this->categoriesCache, (int)$product->getId());
        return true;
    }

    /**
     * Save Product Categories Data
     *
     * @param array $categoriesData
     * @param int $productId
     * @return void
     */
    protected function _saveProductCategories(array $categoriesData, int $productId): void
    {
        static $tableName = null;

        if (!$tableName) {
            $tableName = $this->resourceModelFactory->create()->getProductCategoryTable();
        }
        if ($categoriesData) {
            $categoriesIn = [];
            $delProductId = [];

            foreach ($categoriesData as $delSku => $categories) {
                $delProductId[] = $productId;

                foreach (array_keys($categories) as $categoryId) {
                    $categoriesIn[] = ['product_id' => $productId, 'category_id' => $categoryId, 'position' => 0];
                }
            }

            $this->connection->delete(
                $tableName,
                $this->connection->quoteInto('product_id IN (?)', $delProductId)
            );
            if ($categoriesIn) {
                $this->connection->insertOnDuplicate($tableName, $categoriesIn, ['product_id', 'category_id']);
            }
        }
    }

    /**
     * Get Category Id by External|Visma|SITE Category ID
     *
     * @param int $externalCategoryId
     * @return mixed
     */
    public function getCategoryIdByExternalCategoryId(int $externalCategoryId): mixed
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('madhat_ex_cat_id', $externalCategoryId)->create();

        $categories = $this->categoryList->getList($searchCriteria)->getItems();

        if (count($categories) > 0) {
            $category = array_shift($categories);
            return $category->getId();
        } else {
            return false;
        }
    }

    /**
     * Process ProductAttributes for Product
     *
     * @param ProductDataInterface $productData
     * @param ProductInterface|Product $product
     * @return ProductInterface|Product
     * @throws LocalizedException
     */
    protected function processProductAttributes(
        ProductDataInterface     $productData,
        ProductInterface|Product $product
    ): ProductInterface|Product {
        // Brand
        $product = $this->setBrandValue($product, $productData);

        $product = $this->setBaseColorValue($product, $productData);
        return $product;
    }

    /**
     * Process Variant Attributes for Product
     *
     * @param ProductDataInterface $productData
     * @param ProductInterface|Product $product
     * @return ProductInterface|Product
     * @throws InputException
     * @throws LocalizedException
     * @throws StateException
     */
    private function processVariantAttributes(
        ProductDataInterface     $productData,
        ProductInterface|Product $product
    ): ProductInterface|Product {
        $variantAttributes = $this->madHatProductHelper->getVariantAttributesForProduct(
            $productData,
            $this->attributeSetMapping
        );
        foreach ($productData->getVariantAttributes() as $variantAttribute) {
            if (in_array($variantAttribute->getName(), $variantAttributes)) {
                $product = $this->setVariantAttribute(
                    $product,
                    $productData,
                    $variantAttribute
                );
            }
        }

        return $product;
    }

    /**
     * Get array of Variant Attribute's for Attribute Set
     *
     * @param string $attributeSetName
     * @return mixed
     */
    public function getVariantAttributes(string $attributeSetName): mixed
    {
        if (empty($this->attributeSetMapping)) {
            $this->attributeSetMapping = $this->madHatProductHelper->getAttributeSetMapping();
        }
        foreach ($this->attributeSetMapping as $attributeSetData) {
            if ($attributeSetData['attribute_set'] == $attributeSetName) {
                return $attributeSetData['variant_attributes'];
            }
        }
        return null;
    }

    /**
     * Validate Product Data
     *
     * @param ProductDataInterface $productData
     * @return false
     */
    protected function validateProductData(ProductDataInterface $productData): bool
    {
        $flag = $this->validateBasicData($productData);
        if ($productData->getProductClassValue() == self::REGULAR_PRODUCT_VALUE) {
            return $flag;
        } else {
            $attributeSetName = $this->madHatProductHelper->getAttributeSetName(
                $productData,
                $this->attributeSetMapping
            );
            if (empty($attributeSetName)) {
                $this->productImportErrors[] = [
                    'ProductNo' => $productData->getProductNo(),
                    'ErrorMessage' => 'Incorrect value for ProductType or ProductSubType.',
                ];
                $flag = false;
            }
        }
        if ($flag && $productData->getProductClassValue() == self::VARIANT_PRODUCT_VALUE) {
            $flag = $this->validateVariantAttributeData($productData);
        }
        return $flag;
    }

    /**
     * Validate basic properties of product data for mandatory fields
     *
     * @param ProductDataInterface $productData
     * @return bool
     */
    private function validateBasicData(ProductDataInterface $productData): bool
    {
        if ($productData->getProductNo() == null) {
            $this->productImportErrors[] = [
                'ProductNo' => $productData->getProductNo(),
                'ErrorMessage' => 'Product No value missing'
            ];
            return false;
        }
        if ($productData->getProductClassValue() === null) {
            $this->productImportErrors[] = [
                'ProductNo' => $productData->getProductNo(),
                'ErrorMessage' => 'Product Class Value value missing'
            ];
            return false;
        }
        if ($productData->getName() == null) {
            $this->productImportErrors[] = [
                'ProductNo' => $productData->getProductNo(),
                'ErrorMessage' => 'Name value missing'
            ];
            return false;
        }
        return true;
    }

    /**
     * Validate mandatory variant attribute date of product data
     *
     * @param ProductDataInterface $productData
     * @return bool
     */
    private function validateVariantAttributeData(ProductDataInterface $productData): bool
    {
        $productType = $productData->getProductType();
        if (in_array($productType, [105,107])) {
            $productType = $productData->getProductSubType();
        }

        if (!isset($this->attributeSetMapping[$productType])) {
            $this->productImportErrors[] = [
                'ProductNo' => $productData->getProductNo(),
                'ErrorMessage' => 'Attribute Set Mapping for not configured for Product Type : ' . $productType
            ];
            return false;
        }

        $variantAttributeList = $this->attributeSetMapping[$productType]['variant_attributes'];
        foreach ($productData->getVariantAttributes() as $variantAttribute) {
            if (in_array($variantAttribute->getName(), $variantAttributeList)) {
                $variantAttributeValue = $variantAttribute->getValue();
                if ($variantAttribute->getName() == 'VariantWeight') {
                    $variantAttributeValue = $this->madHatProductHelper->fixVariantWeight(
                        $variantAttributeValue,
                        false
                    );
                }
                if ($variantAttribute->getName() == 'VariantFilamentSize') {
                    $variantAttributeValue = $this->madHatProductHelper->fixVariantFilamentSize(
                        $variantAttributeValue,
                        false
                    );
                }
                if ($variantAttribute->getName() == 'VariantNozzleDiameter') {
                    $variantAttributeValue = $this->madHatProductHelper->fixVariantNozzleDiameter(
                        $variantAttributeValue,
                        false
                    );
                }
                if ($variantAttribute->getName() == 'VariantVoltage') {
                    $variantAttributeValue = $this->madHatProductHelper->fixVariantVoltage(
                        $variantAttributeValue,
                        false
                    );
                }
                if ($variantAttributeValue != 0) {
                    return true;
                }
            }
        }

        $this->productImportErrors[] = [
            'ProductNo' => $productData->getProductNo(),
            'ErrorMessage' => 'No Variant Attribute value found for Variant Product'
        ];
        return false;
    }

    /**
     * Add DBLogger Record
     *
     * @param int $websiteId
     * @return void
     *
     * @throws LocalizedException
     */
    private function addDbLoggerRecord(int $websiteId): void
    {
        $message = "Total Product's : " . $this->totalProducts;
        $message .= " | Saved Product's : " . $this->savedProducts;
        $message .= " | Failed Product's : " . $this->failedProducts;

        if (!empty($this->productImportErrors)) {
            $message .= " | Errors : " . json_encode($this->productImportErrors);
        }

        $website = $this->storeManager->getWebsite($websiteId);
        $defaultStoreId = $website->getDefaultStore()->getId();
        $this->emulation->startEnvironmentEmulation($defaultStoreId);

        $this->dbLoggerSaver->addRecord(
            'Product Import Report',
            $message,
            'NOTICE',
            LogIdentifierProvider::PRODUCT
        );

        $this->emulation->stopEnvironmentEmulation();
    }

    /**
     * Get array of configurable attribute's code on the basis for array of variant attributes
     *
     * @param array $variantAttributes
     * @return array
     */
    public function getConfigurableAttributes(array $variantAttributes): array
    {
        $configurableAttributes = [];
        foreach ($variantAttributes as $attribute) {
            if (isset($this->variantAttributesMapping[$attribute])) {
                $configurableAttributes[] = $this->variantAttributesMapping[$attribute]['code'];
            }
        }

        return $configurableAttributes;
    }

    /**
     * Get array of options for Attribute Code
     *
     * @param string $attributeCode
     * @return array|mixed
     * @throws LocalizedException
     */
    private function getAttributeOptions(string $attributeCode)
    {
        if (!empty($this->attributeOptions) && isset($this->attributeOptions[$attributeCode])) {
            return $this->attributeOptions[$attributeCode];
        }

        $this->attributeOptions[$attributeCode] = $this->madHatProductHelper->getOptionsForAttribute($attributeCode);
        return $this->attributeOptions[$attributeCode];
    }

    /**
     * Filter out option_id on the basis of Attribute Code & Option Label
     *
     * @param string $attributeCode
     * @param string $value
     * @return int|string
     * @throws LocalizedException
     */
    protected function getAttributeOptionIdByLabel(string $attributeCode, string $value): int|string
    {
        $attributeOptions = $this->getAttributeOptions($attributeCode);
        if (isset($attributeOptions[$attributeCode])) {
            // Iterate through the array of options
            foreach ($attributeOptions[$attributeCode] as $option) {
                if (strtolower($option['value']) == strtolower($value)) {
                    return $option['option_id'];
                }
            }
        }

        $this->attributeOptions[$attributeCode] = $this->madHatProductHelper->getOptionsForAttribute($attributeCode);
        foreach ($this->attributeOptions[$attributeCode] as $option) {
            if (strtolower($option['value']) == strtolower($value)) {
                return $option['option_id'];
            }
        }

        throw new LocalizedException(
            __(
                'Attribute Option Not Found for Attribute Code : %1 and Value : %2',
                $attributeCode,
                $value
            )
        );
    }

    /**
     * Set 'madhat_brand' attribute value for products.
     *
     * @param ProductInterface|Product $product
     * @param ProductDataInterface $productData
     * @return ProductInterface|Product
     * @throws LocalizedException
     */
    protected function setBrandValue(
        ProductInterface|Product $product,
        ProductDataInterface     $productData
    ): ProductInterface|Product {
        $brandValue = 0;
        foreach ($productData->getProductAttributes() as $productAttribute) {
            if ($productAttribute->getName() == 'Brand') {
                $brandValue = $productAttribute->getValue();
                $brandLabel = $productAttribute->getLabel();
                break;
            }
        }
        if ($brandValue == 0) {
            return $product;
        }

        try {
            $optionId = $this->getAttributeOptionIdByLabel('madhat_brand', $brandLabel);
        } catch (Exception $e) {
            $optionId = $this->createAttributeOptionIdByLabelForVisualSwatch('madhat_brand', $brandLabel);
        }

        $product->setData('madhat_brand', $optionId);

        return $product;
    }

    /**
     * Save 'simple_preselect' value for configurable product.
     * 'simple_preselect' value is Primary Variant Product SKU of Configurable Product.
     *
     * @return void
     */
    protected function assignPrimaryVariantToConfigurableProduct(): void
    {
        if (!empty($this->primaryVariantArray)) {
            foreach ($this->primaryVariantArray as $baseProductSku => $primaryVariantSku) {
                try {
                    $baseProduct = $this->productRepository->get($baseProductSku, false, Store::DEFAULT_STORE_ID, true);
                    $childProducts = $this->linkManagement->getChildren($baseProductSku);

                    if (empty($childProducts)) {
                        continue;
                    }
                    if ($baseProduct) {
                        $variantProduct = $this->productRepository->get($primaryVariantSku, false, Store::DEFAULT_STORE_ID, true);
                        if ($variantProduct && in_array($variantProduct->getId(), $childProducts)) {
                            $baseProduct->setData('simple_preselect', $primaryVariantSku);
                        } else {
                            $baseProduct->setData('simple_preselect', '');
                        }
                        $this->productRepository->save($baseProduct);
                    }
                } catch (Exception $e) {
                    $this->productImportErrors[] = [
                        'ProductNo' => $baseProductSku,
                        'ErrorMessage' => 'Product not found for Primary Variant Mapping',
                    ];
                }
            }
        }
    }

    /**
     * Create option for Visual Swatch type Attribute.
     *
     * @param string $attributeCode
     * @param string|null $optionLabel
     * @return int|string
     * @throws LocalizedException
     */
    protected function createAttributeOptionIdByLabelForVisualSwatch(
        string  $attributeCode,
        ?string $optionLabel
    ): int|string {
        $attributeModel = $this->attributeFactory->create()->loadByCode(
            Product::ENTITY,
            $attributeCode
        );
        $optionData = [];
        if ($attributeCode == 'madhat_color') {
            $optionData['swatchvisual']['value']['option_0'] = $this->madHatProductHelper->getColorCode($optionLabel);
        } else {
            $optionData['swatchvisual']['value']['option_0'] = '';
        }
        $optionData['optionvisual']['value']['option_0'] = [$optionLabel];
        $attributeModel->addData($optionData);
        $attributeModel->save();

        return $this->getAttributeOptionIdByLabel($attributeCode, $optionLabel);
    }

    /**
     * Create option for Text Swatch type Attribute.
     *
     * @param string $attributeCode
     * @param string|null $optionLabel
     * @return int|string
     * @throws LocalizedException
     */
    protected function createAttributeOptionIdByLabelForTextSwatch(
        string  $attributeCode,
        ?string $optionLabel
    ): int|string {
        $attributeModel = $this->attributeFactory->create()->loadByCode(
            Product::ENTITY,
            $attributeCode
        );

        $optionData = [];
        $optionData['swatchtext']['value']['option_0'] = [$optionLabel];
        $optionData['optiontext']['value']['option_0'] = [$optionLabel];
        $attributeModel->addData($optionData);
        $attributeModel->save();

        return $this->getAttributeOptionIdByLabel($attributeCode, $optionLabel);
    }

    /**
     * Create option for Dropdown type Attribute.
     *
     * @param string $attributeCode
     * @param string|null $optionLabel
     * @return int|string
     * @throws InputException
     * @throws LocalizedException
     * @throws StateException
     */
    protected function createAttributeOptionIdByLabelForDropDown(
        string  $attributeCode,
        ?string $optionLabel
    ): int|string {
        $attributeModel = $this->attributeFactory->create()->loadByCode(
            Product::ENTITY,
            $attributeCode
        );

        $option = ['label' => (string)$optionLabel];
        $optionData = $this->attributeOptionInterfaceFactory->create();
        $optionData->setLabel($option['label']);
        $this->attributeOptionManagement->add(
            ProductAttributeInterface::ENTITY_TYPE_CODE,
            $attributeCode,
            $optionData
        );

        return $this->getAttributeOptionIdByLabel($attributeCode, $optionLabel);
    }

    /**
     * Unset Categories from Products.
     *
     * @throws CouldNotSaveException
     * @throws StateException
     * @throws InputException
     */
    protected function unsetCategoryFromProduct(bool|ProductInterface $product): void
    {
        $currentCategoryIds = $product->getCategoryIds();
        $ignoredCategoryIds = $this->madHatProductHelper->getIgnoredCategoryIdsFromSystemConfig();

        $filteredCategoryIds = array_diff($currentCategoryIds, $ignoredCategoryIds);

        foreach ($filteredCategoryIds as $categoryId) {
            $this->categoryLinkRepository->deleteByIds($categoryId, $product->getSku());
        }
    }

    /**
     * Get Product Attribute Data by Name{Key} - Value pair
     *
     * @param ProductDataInterface $item
     * @param string $productAttributeKey
     * @return mixed
     */
    protected function getDataFromProductAttributes(ProductDataInterface $item, string $productAttributeKey): mixed
    {
        foreach ($item->getProductAttributes() as $productAttribute) {
            if ($productAttributeKey == $productAttribute->getName()) {
                return $productAttribute->getValue();
            }
        }
        return null;
    }

    /**
     * Store Related, UpSell, CrossSell Products link.
     *
     * @param ProductDataInterface $item
     *
     * @return void
     */
    protected function storeProductRelations(ProductDataInterface $item): void
    {
        $relatedProducts = (string)$this->getDataFromProductAttributes($item, 'Accessories');
        $relatedProducts = array_values(array_filter(array_map('trim', explode(',', $relatedProducts))));
        if (!empty($relatedProducts)) {
            $this->productRelationsArray[$item->getProductNo()]['related'] = $relatedProducts;
        }

        $upSellProducts = (string)$this->getDataFromProductAttributes($item, 'SpareParts');
        $upSellProducts = array_values(array_filter(array_map('trim', explode(',', $upSellProducts))));
        if (!empty($upSellProducts)) {
            $this->productRelationsArray[$item->getProductNo()]['upSell'] = $upSellProducts;
        }

        $crossSellProducts = (string)$this->getDataFromProductAttributes($item, 'CrossSells');
        $crossSellProducts = array_values(array_filter(array_map('trim', explode(',', $crossSellProducts))));
        if (!empty($crossSellProducts)) {
            $this->productRelationsArray[$item->getProductNo()]['crossSell'] = $crossSellProducts;
        }
    }

    protected function setProductRelationsForProducts(): void
    {
        if (!empty($this->productRelationsArray)) {
            foreach ($this->productRelationsArray as $productSku => $productRelationData) {
                try {
                    $productModel = $this->productRepository->get($productSku, false, Store::DEFAULT_STORE_ID, true);
                    $productModel = $this->productFactory->create()->load($productModel->getId());
                    $productModel = $this->setProductRelations($productModel);
                    $productModel->setStoreId(Store::DEFAULT_STORE_ID);
                    $this->productRepository->save($productModel);
                } catch (\Exception $e) {
                    $this->logger->notice("Error occurred while setting product relation : " . $e->getMessage());
                }
            }
        }
    }

    /**
     * Set Related, UpSell, and CrossSell product links.
     *
     * @param ProductInterface $product
     * @return ProductInterface
     */
    protected function setProductRelations(ProductInterface $product): ProductInterface
    {
        $sku = $product->getSku();

        if (isset($this->productRelationsArray[$sku])) {
            $relations = $this->productRelationsArray[$sku];

            if (isset($relations['related'])) {
                $relatedProducts = $relations['related'];
                $this->setRelatedProducts($product, $relatedProducts);
            }

            if (isset($relations['upSell'])) {
                $upSellProducts = $relations['upSell'];
                $this->setUpSellProducts($product, $upSellProducts);
            }

            if (isset($relations['crossSell'])) {
                $crossSellProducts = $relations['crossSell'];
                $this->setCrossSellProducts($product, $crossSellProducts);
            }
        }

        return $product;
    }

    /**
     * Filter available SKUs from the provided array
     *
     * @param array $skuArray
     * @return array
     */
    public function getAvailableSkus(array $skuArray): array
    {
        $productCollection = $this->productCollectionFactory->create();
        $productCollection->addFieldToFilter('sku', ['in' => $skuArray]);

        return $productCollection->getColumnValues('sku');
    }

    /**
     * Set Related Products link.
     *
     * @param ProductInterface $productModel
     * @param array $relatedProductSkus
     * @return ProductInterface
     */
    public function setRelatedProducts(ProductInterface $productModel, array $relatedProductSkus): ProductInterface
    {
        try {
            // Start from existing links to avoid overwriting other link types
            $links = $productModel->getProductLinks() ?: [];
            $availableRelatedProductSkus = $this->getAvailableSkus($relatedProductSkus);
            foreach ($availableRelatedProductSkus as $relatedProductSku) {
                // Prevent duplicates for the same sku + link type
                $exists = false;
                foreach ($links as $existingLink) {
                    if ($existingLink->getLinkType() === 'related' && $existingLink->getLinkedProductSku() === $relatedProductSku) {
                        $exists = true;
                        break;
                    }
                }
                if (!$exists) {
                    $productLink = $this->productLinkFactory->create()
                        ->setSku($productModel->getSku())
                        ->setLinkedProductSku($relatedProductSku)
                        ->setPosition(1)
                        ->setLinkType('related');
                    $links[] = $productLink;
                }
            }
            $productModel->setProductLinks($links);
        } catch (\Exception $e) {
            $this->logger->notice("Error occurred while setting related products : " . $e->getMessage());
        }
        return $productModel;
    }

    /**
     * Set UpSell Products link.
     *
     * @param ProductInterface $productModel
     * @param array $upsellProductSkus
     * @return ProductInterface
     */
    public function setUpSellProducts(ProductInterface $productModel, array $upsellProductSkus): ProductInterface
    {
        try {
            // Start from existing links to avoid overwriting other link types
            $links = $productModel->getProductLinks() ?: [];
            $availableUpsellProductSkus = $this->getAvailableSkus($upsellProductSkus);
            foreach ($availableUpsellProductSkus as $upsellProductSku) {
                // Prevent duplicates for the same sku + link type
                $exists = false;
                foreach ($links as $existingLink) {
                    if ($existingLink->getLinkType() === 'upsell' && $existingLink->getLinkedProductSku() === $upsellProductSku) {
                        $exists = true;
                        break;
                    }
                }
                if (!$exists) {
                    $productLink = $this->productLinkFactory->create()
                        ->setSku($productModel->getSku())
                        ->setLinkedProductSku($upsellProductSku)
                        ->setPosition(1)
                        ->setLinkType('upsell');
                    $links[] = $productLink;
                }
            }
            $productModel->setProductLinks($links);
        } catch (\Exception $e) {
            $this->logger->notice("Error occurred while setting UpSell products : " . $e->getMessage());
        }
        return $productModel;
    }

    /**
     * Set CrossSell Products link.
     *
     * @param ProductInterface $productModel
     * @param array $crossSellProductSkus
     * @return ProductInterface
     */
    public function setCrossSellProducts(ProductInterface $productModel, array $crossSellProductSkus): ProductInterface
    {
        try {
            // Start from existing links to avoid overwriting other link types
            $links = $productModel->getProductLinks() ?: [];
            $availableCrossSellProductSkus = $this->getAvailableSkus($crossSellProductSkus);
            foreach ($availableCrossSellProductSkus as $crossSellProductSku) {
                // Prevent duplicates for the same sku + link type
                $exists = false;
                foreach ($links as $existingLink) {
                    if ($existingLink->getLinkType() === 'crosssell' && $existingLink->getLinkedProductSku() === $crossSellProductSku) {
                        $exists = true;
                        break;
                    }
                }
                if (!$exists) {
                    $productLink = $this->productLinkFactory->create()
                        ->setSku($productModel->getSku())
                        ->setLinkedProductSku($crossSellProductSku)
                        ->setPosition(1)
                        ->setLinkType('crosssell');
                    $links[] = $productLink;
                }
            }
            $productModel->setProductLinks($links);
        } catch (\Exception $e) {
            $this->logger->notice("Error occurred while setting Cross-Sell products : " . $e->getMessage());
        }
        return $productModel;
    }

    protected function setBaseColorValue(ProductInterface|Product $product, ProductDataInterface $productData)
    {
        $madhatBaseColorValue = 0;
        foreach ($productData->getProductAttributes() as $productAttribute) {
            if ($productAttribute->getName() == 'Color') {
                $madhatBaseColorValue = $productAttribute->getValue();
                $madhatBaseColorLabel = $productAttribute->getLabel();
                break;
            }
        }
        if ($madhatBaseColorValue == 0) {
            return $product;
        }

        try {
            $optionId = $this->getAttributeOptionIdByLabel('madhat_base_color', $madhatBaseColorLabel);
        } catch (Exception $e) {
            $optionId = $this->createAttributeOptionIdByLabelForVisualSwatch('madhat_base_color', $madhatBaseColorLabel);
        }

        $product->setData('madhat_base_color', $optionId);

        return $product;
    }

    /**
     * Hide products included in Bundled Products
     *
     * @param \MadHat\SiteIntegrationProducts\Api\Data\BundledProductDataInterface[] $bundledProducts
     * @return void
     */
    protected function hideProductsFromFrontend(array $bundledProducts)
    {
        foreach ($bundledProducts as $bundledProduct) {
            $productSku = $bundledProduct->getProductNo();
            try {
                $product = $this->productRepository->get($productSku, false, Store::DEFAULT_STORE_ID, true);
                $product->setVisibility(Visibility::VISIBILITY_NOT_VISIBLE)
                    ->setCategoryIds([])
                    ->setStoreId(Store::DEFAULT_STORE_ID);

                $this->productRepository->save($product);
            } catch (NoSuchEntityException $e) {
                $this->logger->warning(
                    __(
                        'Product with SKU %1 not found during hideProductsFromFrontend.',
                        $productSku
                    )
                );
            } catch (Exception $e) {
                $this->logger->error(
                    __(
                        "Error in hideProductsFromFrontend for SKU {%1} => %2",
                        $productSku,
                        $e->getMessage()
                    )
                );
            }
        }
    }

    /**
     * Get product name from product data or product object with fallback.
     *
     * @param $productData
     * @param $product
     *
     * @return string
     * @throws LocalizedException
     */
    public function getProductName($productData, $product): string
    {
        $productName = $productData ? $productData->getName() : null;

        if (!$productName && $product && $product->getName()) {
            $productName = $product->getName();
        }

        if (!$productName) {
            throw new LocalizedException(
                __("Product Name can't be empty.")
            );
        }

        return $productName;
    }
}
