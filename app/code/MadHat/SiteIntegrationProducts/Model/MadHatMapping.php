<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationProducts\Model;

use MadHat\SiteIntegrationProducts\Api\Data\MadHatMappingInterface;
use MadHat\SiteIntegrationProducts\Api\Data\MadHatMappingInterfaceFactory;
use MadHat\SiteIntegrationProducts\Model\ResourceModel\MadHatMapping\Collection;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Model\Context;
use Magento\Framework\Registry;

class MadHatMapping extends AbstractModel
{
    /**
     * @var MadHatMappingInterfaceFactory
     */
    protected $madhatMappingDataFactory;

    /**
     * @var DataObjectHelper
     */
    protected $dataObjectHelper;

    /**
     * @param Context $context
     * @param Registry $registry
     * @param MadHatMappingInterfaceFactory $madhatMappingDataFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param ResourceModel\MadHatMapping $resource
     * @param Collection $resourceCollection
     * @param array $data
     */
    public function __construct(
        Context $context,
        Registry $registry,
        MadHatMappingInterfaceFactory $madhatMappingDataFactory,
        DataObjectHelper $dataObjectHelper,
        \MadHat\SiteIntegrationProducts\Model\ResourceModel\MadHatMapping $resource,
        Collection $resourceCollection,
        array $data = []
    ) {
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
        $this->madhatMappingDataFactory = $madhatMappingDataFactory;
        $this->dataObjectHelper = $dataObjectHelper;
    }

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\MadHat\SiteIntegrationProducts\Model\ResourceModel\MadHatMapping::class);
    }

    /**
     * @inheritDoc
     */
    public function getMadhatmappingId()
    {
        return $this->getData(self::MADHATMAPPING_ID);
    }

    /**
     * @inheritDoc
     */
    public function setMadhatmappingId($madhatmappingId)
    {
        return $this->setData(self::MADHATMAPPING_ID, $madhatmappingId);
    }

    /**
     * @inheritDoc
     */
    public function getSource()
    {
        return $this->getData(self::SOURCE);
    }

    /**
     * @inheritDoc
     */
    public function setSource($source)
    {
        return $this->setData(self::SOURCE, $source);
    }

    /**
     * @inheritDoc
     */
    public function getDestination()
    {
        return $this->getData(self::DESTINATION);
    }

    /**
     * @inheritDoc
     */
    public function setDestination($destination)
    {
        return $this->setData(self::DESTINATION, $destination);
    }

    /**
     * @inheritDoc
     */
    public function getType()
    {
        return $this->getData(self::TYPE);
    }

    /**
     * @inheritDoc
     */
    public function setType($type)
    {
        return $this->setData(self::TYPE, $type);
    }

    /**
     * @inheritDoc
     */
    public function getCreatedAt()
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * @inheritDoc
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * @inheritDoc
     */
    public function getUpdatedAt()
    {
        return $this->getData(self::UPDATED_AT);
    }

    /**
     * @inheritDoc
     */
    public function setUpdatedAt($updatedAt)
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }

    /**
     * Retrieve madhatMapping model with madhatMapping data
     * @return MadHatMappingInterface
     */
    public function getDataModel()
    {
        $madhatMappingData = $this->getData();

        $madhatMappingDataObject = $this->madhatMappingDataFactory->create();
        $this->dataObjectHelper->populateWithArray(
            $madhatMappingDataObject,
            $madhatMappingData,
            MadHatMappingInterface::class
        );

        return $madhatMappingDataObject;
    }
}
