<?php

namespace MadHat\SiteIntegrationProducts\Helper;

use MadHat\SiteIntegrationProducts\Api\Data\ProductDataInterface;
use MadHat\SiteIntegrationProducts\Model\ResourceModel\Doptions\CollectionFactory as DoptionsCollectionFactory;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\ProductFactory;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory as ProductCollectionFactory;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Eav\Model\Config;
use Magento\Eav\Model\ResourceModel\Entity\Attribute\Set\CollectionFactory as AttributeSetCollectionFactory;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Store\Model\Store;
use Magento\Store\Model\StoreManagerInterface;

class Data extends AbstractHelper
{
    protected const ATTRIBUTE_SET_MAPPING_PATH = 'site/product/product_attribute_set_mapping';

    protected const COLOR_CODE_MAPPING_PATH = 'site/product/color_code_mapping';

    protected const PRODUCT_TAX_CODE_MAPPING_PATH = 'site/product/product_tax_code_mapping';

    protected const PRODUCT_KEEP_EXISTING_LINKED_PATH = 'site/product/keep_existing_linked_products';

    protected const SITE_PRODUCTS_IGNORED_CATEGORY_IDS_CONFIG = 'site/product/ignore_category_ids';

    /**
     * @var DoptionsCollectionFactory
     */
    protected DoptionsCollectionFactory $dOptionsCollectionFactory;

    /**
     * @var ProductRepositoryInterface
     */
    protected ProductRepositoryInterface $productRepository;

    /**
     * @var ProductFactory
     */
    protected ProductFactory $productFactory;

    /**
     * @var ProductCollectionFactory
     */
    protected ProductCollectionFactory $productCollectionFactory;

    /**
     * @var Configurable
     */
    protected Configurable $configurable;

    /**
     * @var Config
     */
    protected Config $eavConfig;

    /**
     * @var AttributeSetCollectionFactory
     */
    protected AttributeSetCollectionFactory $attributeSetCollectionFactory;

    /**
     * @var EavSetupFactory
     */
    protected EavSetupFactory $eavSetupFactory;

    /**
     * @var ResourceConnection
     */
    protected ResourceConnection $resource;

    /**
     * @var Json
     */
    protected Json $jsonSerializer;

    /**
     * @var StoreManagerInterface
     */
    protected StoreManagerInterface $storeManager;

    /**
     * @param Context $context
     * @param DoptionsCollectionFactory $dOptionsCollectionFactory
     * @param ProductRepositoryInterface $productRepository
     * @param ProductFactory $productFactory
     * @param ProductCollectionFactory $productCollectionFactory
     * @param Configurable $configurable
     * @param Config $eavConfig
     * @param AttributeSetCollectionFactory $attributeSetCollectionFactory
     * @param EavSetupFactory $eavSetupFactory
     * @param ResourceConnection $resource
     * @param Json $jsonSerializer
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        Context $context,
        DoptionsCollectionFactory $dOptionsCollectionFactory,
        ProductRepositoryInterface $productRepository,
        ProductFactory $productFactory,
        ProductCollectionFactory $productCollectionFactory,
        Configurable $configurable,
        Config $eavConfig,
        AttributeSetCollectionFactory $attributeSetCollectionFactory,
        EavSetupFactory $eavSetupFactory,
        ResourceConnection $resource,
        Json $jsonSerializer,
        StoreManagerInterface $storeManager
    ) {
        parent::__construct($context);
        $this->dOptionsCollectionFactory = $dOptionsCollectionFactory;
        $this->productRepository = $productRepository;
        $this->productFactory = $productFactory;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->configurable = $configurable;
        $this->eavConfig = $eavConfig;
        $this->attributeSetCollectionFactory = $attributeSetCollectionFactory;
        $this->eavSetupFactory = $eavSetupFactory;
        $this->resource = $resource;
        $this->jsonSerializer = $jsonSerializer;
        $this->storeManager = $storeManager;
    }

    /**
     * Get table object for table name
     *
     * @param $tableName
     * @return string
     */
    public function getTable($tableName): string
    {
        return $this->resource->getTableName($tableName);
    }

    /**
     * Get attribute ID by attribute code.
     *
     * @param string $attributeCode
     * @return int|null
     * @throws LocalizedException
     */
    public function getAttributeIdByCode(string $attributeCode): ?int
    {
        $attribute = $this->eavConfig->getAttribute(\Magento\Catalog\Model\Product::ENTITY, $attributeCode);
        return $attribute->getId();
    }

    /**
     * @return array|bool|float|int|mixed|string|null
     */
    public function getAttributeSetMapping()
    {
        $attributeSetMapping = $this->scopeConfig->getValue(
            self::ATTRIBUTE_SET_MAPPING_PATH
        );
        return $this->jsonSerializer->unserialize($attributeSetMapping);
    }

    /**
     * @param ProductDataInterface $productData
     * @return string
     */
    public function generateUrlKey(ProductDataInterface $productData): string
    {
        $urlKey = strtolower($productData->getName());
        $urlKey = preg_replace('/[^a-z0-9]+/i', '-', $urlKey);
        $urlKey = trim($urlKey, '-');
        $sku = strtolower($productData->getProductNo());
        $urlKey .= '-' . $sku;

        return $urlKey;
    }

    /**
     * Get options array for attribute code
     *
     * @param string $attributeCode
     * @param int $storeId
     * @return array
     * @throws LocalizedException
     */
    public function getOptionsForAttribute(string $attributeCode, int $storeId = 0): array
    {
        $attributeId = $this->getAttributeIdByCode($attributeCode);
        if (!$attributeId) {
            return [];
        }

        $connection = $this->resource->getConnection();
        $optionTable = $this->getTable('eav_attribute_option');
        $optionValueTable = $this->getTable('eav_attribute_option_value');

        $select = $connection->select()
            ->from(['eao' => $optionTable], [])
            ->join(
                ['eaov' => $optionValueTable],
                'eao.option_id = eaov.option_id',
                ['option_id' => 'eaov.option_id', 'value' => 'eaov.value']
            )
            ->where('eao.attribute_id = ?', $attributeId)
            ->where('eaov.store_id = ?', $storeId);

        return $connection->fetchAll($select);
    }

    /**
     * @param string $attributeSetName
     * @return int|string
     * @throws LocalizedException
     */
    public function getAttributeSetId(string $attributeSetName): int|string
    {
        $eavSetup = $this->eavSetupFactory->create();
        $entityTypeId = $eavSetup->getEntityTypeId(Product::ENTITY);

        $attributeSetCollection = $this->attributeSetCollectionFactory->create()
            ->addFieldToFilter('attribute_set_name', $attributeSetName)
            ->addFieldToFilter('entity_type_id', $entityTypeId)
            ->setPageSize(1)
            ->load();

        if ($attributeSetCollection->count()) {
            $attributeSet = $attributeSetCollection->getFirstItem();
            $attributeSetId = $attributeSet->getId();
        } else {
            $attributeSetId = $this->productFactory->create()->getDefaultAttributeSetId();
        }

        return $attributeSetId;
    }

    public function getVariantAttributesForProduct(ProductDataInterface $productData, array $attributeSetMapping)
    {
        $productType = $productData->getProductType();
        $productSubType = $productData->getProductSubType();

        $variantAttributes = [];
        if ($productType && isset($attributeSetMapping[$productType])) {
            $variantAttributes = $attributeSetMapping[$productType]['variant_attributes'];
        } elseif ($productSubType && isset($attributeSetMapping[$productSubType])) {
            $variantAttributes = $attributeSetMapping[$productSubType]['variant_attributes'];
        }

        return $variantAttributes;
    }

    /**
     * @param ProductDataInterface $productData
     * @param array $attributeSetMapping
     * @return mixed
     */
    public function getAttributeSetName(ProductDataInterface $productData, array $attributeSetMapping)
    {
        $productType = $productData->getProductType();
        $productSubType = $productData->getProductSubType();

        $attributeSetName = '';
        if ($productType && isset($attributeSetMapping[$productType])) {
            $attributeSetName = $attributeSetMapping[$productType]['attribute_set'];
        } elseif ($productSubType && isset($attributeSetMapping[$productSubType])) {
            $attributeSetName = $attributeSetMapping[$productSubType]['attribute_set'];
        }

        return $attributeSetName;
    }

    public function fixVariantFilamentSize(?string $variantAttributeValue, $unit = true): string
    {
        $number = str_replace(',', '.', $variantAttributeValue);
        $formattedNumber = number_format((float)$number, 2, '.', '');

        if ($unit) {
            return $formattedNumber . ' mm';
        }

        return $formattedNumber;
    }

    public function fixVariantWeight(?string $weightValue, $unit = true): string
    {
        $parts = explode(',', $weightValue);

        $valueInGrams = (float)$parts[0];
        if ($valueInGrams < 1000) {
            $weight = $valueInGrams;
            if ($unit) {
                $weight .= " g";
            }

        } else {
            $weight = $this->getWeightValue($valueInGrams);
            if ($unit) {
                $weight .= " kg";
            }

        }

        return $weight;
    }

    /**
     * @param string|null $variantAttributeName
     * @param string|null $variantAttributeValue
     * @return bool
     */
    public function getVariantAttributedOption(
        ?string $variantAttributeName,
        ?string $variantAttributeValue
    ): bool {
        $dOptionsCollection = $this->dOptionsCollectionFactory->create()
            ->addFieldToFilter('type', ['eq' => $variantAttributeName])
            ->addFieldToFilter('value', ['eq' => $variantAttributeValue]);

        if ($dOptionsCollection->count()) {
            return true;
        }
        return false;
    }

    /**
     * @param string $colorString
     * @return string
     */
    public function getColorCode(string $colorString): string
    {
        $colorCodeMapping = $this->getColorCodeMapping();

        $defaultColorCode = $colorStringMapping['default'] ?? '#ffffff';
        $colorStringArray = explode(" ", strtolower($colorString));
        foreach ($colorStringArray as $colorSubStr) {
            if (array_key_exists($colorSubStr, $colorCodeMapping)) {
                return $colorCodeMapping[$colorSubStr];
            }
        }
        return $defaultColorCode;
    }

    /**
     * @return array
     */
    public function getColorCodeMapping(): array
    {
        $colorCodeMapping = $this->scopeConfig->getValue(
            self::COLOR_CODE_MAPPING_PATH
        );
        return $this->jsonSerializer->unserialize($colorCodeMapping);
    }

    /**
     * Return Array of Product Tax Code Mapping
     *
     * @return array
     */
    public function getProductTaxCodeMapping(): array
    {
        $productTaxCodeMapping = $this->scopeConfig->getValue(
            self::PRODUCT_TAX_CODE_MAPPING_PATH
        );

        if (empty($productTaxCodeMapping)) {
            return [];
        }

        return $this->jsonSerializer->unserialize($productTaxCodeMapping);
    }

    /**
     * Return config value for keep_existing_linked_products
     *
     * @return bool
     */
    public function getKeepExistingLinkedProducts(): bool
    {
        return (bool) $this->scopeConfig->getValue(
            self::PRODUCT_KEEP_EXISTING_LINKED_PATH
        );
    }

    /**
     * Get weight value in kgs
     *
     * @param int|float $weightInGrams
     * @return int|float
     */
    public function getWeightValue(int|float $weightInGrams): int|float
    {
        return $weightInGrams / 1000;
    }

    public function getBaseProductCollection(mixed $productSkus = []): \Magento\Catalog\Model\ResourceModel\Product\Collection
    {
        $productCollection = $this->productCollectionFactory->create()
            ->addAttributeToFilter('type_id', 'configurable');

        if (!empty($productSkus)) {
            $productCollection->addFieldToFilter('sku', ['in' => $productSkus]);
        }

        $productCollection->addAttributeToFilter('description', ['null' => true])
            ->addAttributeToFilter('short_description', ['null' => true])
            ->addAttributeToFilter('simple_preselect', ['notnull' => true]);

        return $productCollection;
    }

    /**
     * Copy Primary Variant Data to Base Product
     *
     * @param string $baseProductSku
     * @return array
     */
    public function copyDataFromPrimaryVariantToBase(string $baseProductSku): array
    {
        try {
            $baseProduct = $this->productRepository->get($baseProductSku, false, Store::DEFAULT_STORE_ID, true);
            $primaryVariantProductSku = $baseProduct->getData('simple_preselect');

            if (!empty($primaryVariantProductSku)) {
                $variantProduct = $this->productRepository->get($primaryVariantProductSku, false, Store::DEFAULT_STORE_ID, true);

                // Copy data to base product and store in store 0
                $baseProduct->setStoreId(0)
                    ->setShortDescription($variantProduct->getShortDescription())
                    ->setDescription($variantProduct->getDescription())
                    ->setMadhatTechnicalData($variantProduct->getMadhatTechnicalData());

                $this->productRepository->save($baseProduct);

                // Load variants and clear descriptions
                $baseProductModel = $this->productFactory->create()->load($baseProduct->getId());
                $variantProducts = $this->configurable->getUsedProducts($baseProductModel);

                foreach ($variantProducts as $variantProduct) {
                    $variantProduct->setStoreId(0)
                        ->setShortDescription('')
                        ->setDescription('')
                        ->setMadhatTechnicalData('');
                    $this->productRepository->save($variantProduct);
                }

                return [
                    'status' => 'success',
                    'message' => __('Data copied successfully')
                ];
            }

            return [
                'status' => 'error',
                'message' => __('Primary Variant Product not found')
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    public function fixVariantNozzleDiameter(?string $variantAttributeValue, $unit = true): string
    {
        $number = str_replace(',', '.', $variantAttributeValue);
        $formattedNumber = number_format((float)$number, 2, '.', '');

        if ($unit) {
            return $formattedNumber . ' mm';
        }

        return $formattedNumber;
    }

    public function fixVariantVoltage(?string $variantAttributeValue, $unit = true): string
    {
        if ((int)$variantAttributeValue > 0) {
            return $variantAttributeValue . ' V';
        }

        return $variantAttributeValue;
    }

    /**
     * Return array of ignored category id's from system config.
     *
     * @return array
     */
    public function getIgnoredCategoryIdsFromSystemConfig():array
    {
        $ignoredCategoryIds = [];
        $ignoredCategoryIdsString = $this->scopeConfig->getValue(
            self::SITE_PRODUCTS_IGNORED_CATEGORY_IDS_CONFIG
        );

        if (!empty($ignoredCategoryIdsString)) {
            $ignoredCategoryIds = explode(',', $ignoredCategoryIdsString);
        }

        return $ignoredCategoryIds;
    }
}
