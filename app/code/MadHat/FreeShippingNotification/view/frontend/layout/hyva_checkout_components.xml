<?xml version="1.0"?>
<!--
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2022-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <block name="free.shipping.notification" as="free-shipping-notification" template="MadHat_FreeShippingNotification::notification.phtml">
            <arguments>
                <argument name="view_model"
                          xsi:type="object">MadHat\FreeShippingNotification\ViewModel\FreeShipping</argument>
            </arguments>
        </block>
        <move element="free.shipping.notification" destination="column.right" before="checkout.quote-summary.section"/>
    </body>
</page>

