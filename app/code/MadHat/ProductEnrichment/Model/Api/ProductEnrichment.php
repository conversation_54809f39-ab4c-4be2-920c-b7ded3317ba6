<?php
declare(strict_types=1);

namespace MadHat\ProductEnrichment\Model\Api;

use MadHat\DbLogger\Logger\DbLoggerSaver;
use MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider;
use MadHat\ProductEnrichment\Helper\Data;
use MadHat\ProductEnrichment\Logger\Logger;
use Magento\Catalog\Model\ProductRepository;
use Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\StateException;
use Magento\Framework\Filesystem;
use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\Xml\Parser;
use Magento\Store\Model\App\Emulation;
use Magento\Store\Model\Store;
use Magento\Store\Model\StoreManagerInterface;

class ProductEnrichment
{
    private DbLoggerSaver $dbLoggerSaver;
    private Data $productEnrichmentHelper;
    private Logger $productEnrichmentLogger;
    private ProductRepository $productRepository;
    private Configurable $configurableModel;
    private Filesystem $filesystem;
    private Curl $curlClient;
    private Json $json;
    private Parser $xmlParser;
    private Emulation $emulation;
    private StoreManagerInterface $storeManager;

    public function __construct(
        DbLoggerSaver $dbLoggerSaver,
        Data $productEnrichmentHelper,
        Logger $productEnrichmentLogger,
        ProductRepository $productRepository,
        Configurable $configurableModel,
        Filesystem $filesystem,
        Curl $curlClient,
        Json $json,
        Parser $xmlParser,
        Emulation $emulation,
        StoreManagerInterface $storeManager
    ) {
        $this->dbLoggerSaver = $dbLoggerSaver;
        $this->productEnrichmentHelper = $productEnrichmentHelper;
        $this->productEnrichmentLogger = $productEnrichmentLogger;
        $this->productRepository = $productRepository;
        $this->configurableModel = $configurableModel;
        $this->filesystem = $filesystem;
        $this->curlClient = $curlClient;
        $this->json = $json;
        $this->xmlParser = $xmlParser;
        $this->emulation = $emulation;
        $this->storeManager = $storeManager;
    }

    public function processProductEnrichment(): bool|array
    {
        $isEnabled = $this->productEnrichmentHelper->getIsEnabledFromConfig();
        if (!$isEnabled) {
            $this->productEnrichmentLogger->info("ProductEnrichment is disabled. Please enable for product enrichment.");
            return false;
        }

        $sourceUrl = $this->productEnrichmentHelper->getSourceUrlFromConfig();
        if (empty($sourceUrl)) {
            $this->productEnrichmentLogger->info("ProductEnrichment Source URL is empty.");
            return false;
        }

        $productEnrichmentXmlContent = $this->downloadFileFromSourceUrl($sourceUrl);
        if (!$productEnrichmentXmlContent) {
            $this->productEnrichmentLogger->info("Failed to download or validate XML file.");
            return false;
        }

        $productEnrichmentProcessType = $this->productEnrichmentHelper->getEnrichmentTypeFromConfig();

        $this->xmlParser->loadXML($productEnrichmentXmlContent);
        $arrayData = $this->xmlParser->xmlToArray();

        $entities = $arrayData['root']['entity'] ?? [];

        $dbLoggerReport = [
            'total_products' => count($entities),
            'success_products' => 0,
            'failed_products' => 0,
            'error_messages' => []
        ];

        foreach ($entities as $entity) {
            $productEnrichStatus = $this->enrichProduct($entity, $productEnrichmentProcessType);
            if (!$productEnrichStatus['success']) {
                $dbLoggerReport['failed_products']++;
                $dbLoggerReport['error_messages'][] = [
                    'SKU' => $entity['SKU'],
                    'ErrorMessage' => $productEnrichStatus['error_message']
                ];
            } else {
                $dbLoggerReport['success_products']++;
            }
        }

        $this->logReportToDbLogger($dbLoggerReport);

        return $dbLoggerReport;
    }

    private function downloadFileFromSourceUrl(string $sourceUrl): bool|string
    {
        try {
            $this->curlClient->get($sourceUrl);
            $status = $this->curlClient->getStatus();
            $response = $this->curlClient->getBody();

            if ($status !== 200 || empty($response)) {
                $this->productEnrichmentLogger->error("Failed to download XML. HTTP Status Code: $status");
                return false;
            }

            // Validate XML using Magento's parser (throws exception on failure)
            $this->xmlParser->loadXML($response); // Throws exception if invalid

            return $response;
        } catch (\Exception $e) {
            $this->productEnrichmentLogger->error("Error during XML fetch or validation: " . $e->getMessage());
            return false;
        }
    }

    protected function enrichProduct(
        array $entity,
        string $productEnrichmentProcessType
    ): array {
        $result = [
            'success' => true,
            'error_message' => null,
        ];
        try {
            $this->productEnrichmentLogger->info(__("Product SKU [%1] enrichment starts.", $entity['SKU']));
            $product = $this->productRepository->get($entity['SKU'], false, Store::DEFAULT_STORE_ID, true);

            $productLastEnrichedAt = $product->getLastEnrichedAt();
            $entityTimestamp = strtotime($entity['variationUpdatedAt']);

            if (!$productLastEnrichedAt || strtotime($productLastEnrichedAt) < $entityTimestamp) {
                if ($productEnrichmentProcessType == 'texts_only') {
                    // Process Product Texts
                    $this->processProductTexts($product, $entity);
                } elseif ($productEnrichmentProcessType == 'images_only') {
                    // Process Product Images
                    $this->processProductImages($product, $entity);
                } else {
                    // Process Product Texts and Images both
                    $this->processProductTexts($product, $entity);
                    $this->processProductImages($product, $entity);
                }
            } else {
                $this->productEnrichmentLogger->error(
                    __("Failed to enrich product [ %1 ]: Product data is not updated.", $entity['SKU'])
                );
                return [
                    'success' => false,
                    'error_message' => 'Product data is not updated',
                ];
            }
            $this->productEnrichmentLogger->info(__("Product SKU [%1] enrichment ends.", $entity['SKU']));
        } catch (\Exception $e) {
            $this->productEnrichmentLogger->error(
                __("Failed to enrich product [ %1 ]. Exception : %2", $entity['SKU'], $e->getMessage())
            );
            $result = [
                'success' => false,
                'error_message' => $e->getMessage(),
            ];
        }

        return $result;
    }

    /**
     * @throws NoSuchEntityException
     * @throws CouldNotSaveException
     * @throws StateException
     * @throws InputException
     */
    protected function processProductTexts(mixed $product, array $entity): void
    {
        $entityTimestamp = strtotime($entity['variationUpdatedAt']);
        $entityLastEnrichedAt = date('Y-m-d H:i:s', $entityTimestamp);

        if ($product->getType() == 'simple') {
            $parentIds = $this->configurableModel->getParentIdsByChild($product->getId());
            if (!empty($parentIds)) {
                $configurableProduct = $this->productRepository->getById($parentIds[0]);
                $primaryVariant = $configurableProduct->getCustomAttribute('simple_preselect');
                if ($primaryVariant == $entity['SKU']) {
                    $product->setLastEnrichedProduct($entityLastEnrichedAt);
                    $this->productRepository->save($product);
                    $product = $configurableProduct;
                } else {
                    return;
                }
            }
        }

        $product->setStoreId(Store::DEFAULT_STORE_ID);
        $product->setShortDescription($entity['short_description']);
        $product->setDescription($entity['description']);
        $product->setMadhatTechnicalData($entity['technical_data']);

        $product->setLastEnrichedAt($entityLastEnrichedAt);
        $this->productRepository->save($product);
    }

    protected function processProductImages(mixed $product, array $entity): void
    {
        $baseImage = $product->getData('image');

        $hasValidBaseImage = $baseImage && $baseImage !== 'no_selection';

        if (!$hasValidBaseImage) {
            // Remove existing image for media gallery
            $existingMediaGalleryEntries = $product->getMediaGalleryEntries();
            foreach ($existingMediaGalleryEntries as $key => $entry) {
                unset($existingMediaGalleryEntries[$key]);
            }
            $product->setMediaGalleryEntries($existingMediaGalleryEntries);
            $this->productRepository->save($product);

            // Add new images for product
            $images = [];
            if (is_array($entity['image_list'])) {
                foreach ($entity['image_list'] as $image) {
                    $imagePath = $this->downloadImage($image, $entity['SKU']);
                    $images[] = $imagePath;
                }
            } elseif ($entity['image_list']) {
                $imagePath = $this->downloadImage($entity['image_list'], $entity['SKU']);
                $images[] = $imagePath;
            }
            if (count($images)) {
                $entityTimestamp = strtotime($entity['variationUpdatedAt']);
                $entityLastEnrichedAt = date('Y-m-d H:i:s', $entityTimestamp);
                $product->setLastEnrichedAt($entityLastEnrichedAt);
                $this->attachImagesToProduct($product, $images);
            }
        }
    }

    /**
     * @throws FileSystemException
     */
    protected function downloadImage($url, $sku): string|bool
    {
        $this->productEnrichmentLogger->info(
            __("Downloading image from URL: %1", $url)
        );
        try {
            $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
            $productEnrichmentImagesDir = '/tmp/product_enrichment_images/';

            // Create directory if it doesn't exist
            if (!$mediaDirectory->isDirectory($productEnrichmentImagesDir)) {
                $mediaDirectory->create($productEnrichmentImagesDir, 0775, true);
            }

            $imageName = parse_url($url, PHP_URL_PATH);
            if ($imageName === false) {
                throw new LocalizedException(__("Image can't be downloaded because of invalid name."));
            }
            $originalImageName = basename($imageName);
            $newImageName = "{$sku}-{$originalImageName}";
            $imagePath = $productEnrichmentImagesDir . $newImageName;

            $imageContent = @file_get_contents($url);

            if ($imageContent !== false) {
                $mediaDirectory->writeFile($imagePath, $imageContent, 'w');
                $this->productEnrichmentLogger->info(
                    __("Downloaded image on Path : %1", $imagePath)
                );
                return $imagePath;
            } else {
                $this->productEnrichmentLogger->error("Failed to download image. URL: " . $url);
            }
        } catch (\Exception $e) {
            $this->productEnrichmentLogger->error(
                __("Failed to download image. URL [ %1 ], Exception : %2  ", $url, $e->getMessage())
            );
        }

        return false;
    }

    /**
     * @param $product
     * @param $imagePaths
     * @return void
     * @throws CouldNotSaveException
     * @throws FileSystemException
     * @throws InputException
     * @throws StateException
     */
    private function attachImagesToProduct($product, $imagePaths): void
    {
        if (empty($imagePaths)) {
            return;
        }

        $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
        $productEnrichmentImagesPath = 'tmp/product_enrichment_images/';
        $productEnrichmentImagesDir = $mediaDirectory->getAbsolutePath($productEnrichmentImagesPath);

        $baseImage = array_shift($imagePaths);

        $product->addImageToMediaGallery(
            $productEnrichmentImagesDir . basename($baseImage),
            ['image', 'small_image', 'thumbnail', 'swatch'],
            true,
            false
        );

        foreach ($imagePaths as $imagePath) {
            $product->addImageToMediaGallery(
                $productEnrichmentImagesDir . basename($imagePath),
                null,
                true,
                false
            );
        }
        $this->productRepository->save($product);
    }

    /**
     * Log Report to DbLogger
     *
     * @param array $dbLoggerReport
     * @return void
     */
    public function logReportToDbLogger(array $dbLoggerReport): void
    {
        $title = 'Product Enrichment Report';

        $message = "Total Product's : " . $dbLoggerReport['total_products'];
        $message .= " | Success Product's : " . $dbLoggerReport['success_products'];
        $message .= " | Failed Product's : " . $dbLoggerReport['failed_products'];

        if (!empty($dbLoggerReport['error_messages'])) {
            $message .= " | Errors : " . $this->json->serialize($dbLoggerReport['error_messages']);
        }

        $defaultStoreId = $this->storeManager->getDefaultStoreView()->getId();

        if (!$defaultStoreId) {
            $defaultStoreId = 1;
        }

        $this->emulation->startEnvironmentEmulation($defaultStoreId);
        $this->dbLoggerSaver->addRecord(
            $title,
            $message,
            'NOTICE',
            LogIdentifierProvider::PRODUCT
        );
        $this->emulation->stopEnvironmentEmulation();
    }
}
