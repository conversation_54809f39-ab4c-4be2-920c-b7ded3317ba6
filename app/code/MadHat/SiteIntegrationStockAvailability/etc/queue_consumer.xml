<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
<!--    <consumer name="madhat.siteintegrationstockavailability.consumer" queue="site.techoutlet.stockavailability" connection="amqpSITE"-->
<!--              handler="MadHat\SiteIntegrationStockAvailability\Model\RabbitMQ\StockAvailabilityConsumer::processMessage"/>-->

    <!-- For developer only -->
    <consumer name="madhat.siteintegrationstockavailability.consumer" queue="site.techoutlet-dev.stockavailability.olegh" connection="amqpSITE"
              handler="MadHat\SiteIntegrationStockAvailability\Model\RabbitMQ\StockAvailabilityConsumer::processMessage"/>
</config>
