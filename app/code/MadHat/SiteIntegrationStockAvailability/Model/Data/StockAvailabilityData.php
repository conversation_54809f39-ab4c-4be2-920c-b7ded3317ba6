<?php

namespace MadHat\SiteIntegrationStockAvailability\Model\Data;

use MadHat\SiteIntegrationStockAvailability\Api\Data\StockAvailabilityDataInterface;

class StockAvailabilityData implements StockAvailabilityDataInterface
{
    protected ?string $productNo;
    protected ?string $externalProductNo;
    protected ?string $productStatus;
    protected ?string $availabilityStatus;
    protected ?string $warehouseAvailabilityStatus;
    protected ?int $unitsInStock;
    protected ?int $productStatusValue;
    protected ?int $buyableStatusOnWebsite;
    protected ?int $buyableStatusOnWebsiteValue;
    private ?string $dummyValue;

    public function getProductNo(): ?string
    {
        return $this->productNo;
    }

    public function setProductNo(?string $productNo): void
    {
        $this->productNo = $productNo;
    }

    public function getExternalProductNo(): ?string
    {
        return $this->externalProductNo;
    }

    public function setExternalProductNo(?string $externalProductNo): void
    {
        $this->externalProductNo = $externalProductNo;
    }

    public function getProductStatus(): ?string
    {
        return $this->productStatus;
    }

    public function setProductStatus(?string $productStatus): void
    {
        $this->productStatus = $productStatus;
    }

    public function getAvailabilityStatus(): ?string
    {
        return $this->availabilityStatus;
    }

    public function setAvailabilityStatus(?string $availabilityStatus): void
    {
        $this->availabilityStatus = $availabilityStatus;
    }

    public function getWarehouseAvailabilityStatus(): ?string
    {
        return $this->warehouseAvailabilityStatus;
    }

    public function setWarehouseAvailabilityStatus(?string $warehouseAvailabilityStatus): void
    {
        $this->warehouseAvailabilityStatus = $warehouseAvailabilityStatus;
    }

    public function getUnitsInStock(): ?int
    {
        return $this->unitsInStock;
    }
    public function setUnitsInStock(?int $unitsInStock): void
    {
        $this->unitsInStock = $unitsInStock;
    }

    public function getProductStatusValue(): ?int
    {
        return $this->productStatusValue;
    }

    public function setProductStatusValue(?int $productStatusValue): void
    {
        $this->productStatusValue = $productStatusValue;
    }

    public function getBuyableStatusOnWebsite(): ?int
    {
        return $this->buyableStatusOnWebsite;
    }

    public function setBuyableStatusOnWebsite(?int $buyableStatusOnWebsite): void
    {
        $this->buyableStatusOnWebsite = $buyableStatusOnWebsite;
    }

    public function getBuyableStatusOnWebsiteValue(): ?int
    {
        return $this->buyableStatusOnWebsiteValue;
    }

    public function setBuyableStatusOnWebsiteValue(?int $buyableStatusOnWebsiteValue): void
    {
        $this->buyableStatusOnWebsiteValue = $buyableStatusOnWebsiteValue;
    }

    public function getDummyValue(): ?string
    {
        return $this->dummyValue;
    }

    public function setDummyValue(?string $dummyValue): void
    {
        $this->dummyValue = $dummyValue;
    }
}
