<?php
/**
 * Get StockAvailability data from RabbitMQ
 */

namespace MadHat\SiteIntegrationStockAvailability\Model\RabbitMQ;

use MadHat\InventoryImport\Model\StockStatusModel;
use MadHat\SiteIntegrationStockAvailability\Api\Data\StockAvailabilityDataInterface;
use MadHat\SiteIntegrationStockAvailability\Model\StockAvailabilityModel;
use Psr\Log\LoggerInterface;

class StockAvailabilityConsumer
{
    /** @var LoggerInterface */
    private LoggerInterface $logger;

    /** @var StockStatusModel */
    public StockStatusModel $stockStatusModel;

    /**
     * WhiteRabbitConsumer constructor.
     *
     * @param StockAvailabilityModel $stockAvailabilityModel
     * @param StockStatusModel $stockStatusModel
     * @param LoggerInterface $logger
     */
    public function __construct(
        StockAvailabilityModel $stockAvailabilityModel,
        StockStatusModel $stockStatusModel,
        LoggerInterface $logger
    ) {
        $this->stockStatusModel = $stockStatusModel;
        $this->logger = $logger;
    }

    /**
     * consumer process start
     * @param StockAvailabilityDataInterface[] $message
     * @return void
     */
    public function processMessage(array $message): void
    {
        $this->logger->info(__(
            '%1 => %2 Received Msg from RabbitMQ for Stock update:',
            __CLASS__,
            __FUNCTION__
        ));

        $this->stockStatusModel->updateModifiedProductStatus($message);

        $this->logger->info(__(
            '%1 => %2 RabbitMQ for Stock update is done:',
            __CLASS__,
            __FUNCTION__
        ));
    }

}
