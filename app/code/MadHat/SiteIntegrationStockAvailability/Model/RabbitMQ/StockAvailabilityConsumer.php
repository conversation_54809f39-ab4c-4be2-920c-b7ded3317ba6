<?php
/**
 * Get StockAvailability data from RabbitMQ
 */

namespace MadHat\SiteIntegrationStockAvailability\Model\RabbitMQ;

use MadHat\SiteIntegrationStockAvailability\Model\StockAvailabilityModel;
use MadHat\SiteIntegrationStockAvailability\Model\StockAvailabilityProcessor;
use Magento\Framework\Serialize\Serializer\Json AS SerializerJson;
use MadHat\InventoryImport\Model\StockStatusModel;
use Psr\Log\LoggerInterface;

class StockAvailabilityConsumer
{
    private StockAvailabilityModel $stockAvailabilityModel;

    /** @var LoggerInterface */
    private $logger;

    /** @var StockStatusModel */
    public $stockStatusModel;
    private SerializerJson $jsonSerializer;
    private StockAvailabilityProcessor $stockAvailabilityProcessor;

    /**
     * WhiteRabbitConsumer constructor.
     *
     * @param StockAvailabilityModel $stockAvailabilityModel
     * @param StockAvailabilityProcessor $stockAvailabilityProcessor
     * @param SerializerJson $jsonSerializer
     * @param StockStatusModel $stockStatusModel
     * @param LoggerInterface $logger
     */
    public function __construct(
        StockAvailabilityModel $stockAvailabilityModel,
        StockAvailabilityProcessor $stockAvailabilityProcessor,
        SerializerJson $jsonSerializer,
        StockStatusModel $stockStatusModel,
        LoggerInterface $logger
    ) {
        $this->stockAvailabilityModel = $stockAvailabilityModel;
        $this->stockAvailabilityProcessor = $stockAvailabilityProcessor;
        $this->jsonSerializer = $jsonSerializer;
        $this->stockStatusModel = $stockStatusModel;
        $this->logger = $logger;
    }

    /**
     * consumer process start
     * @param \MadHat\SiteIntegrationStockAvailability\Api\Data\StockAvailabilityDataInterface[] $message
     * @return void
     */
    public function processMessage(array $message): void
    {
         $this->logger->info(__('%1 => %2 Received Msg from RabbitMQ for Stock update:',
             __CLASS__,
             __FUNCTION__
         ));

        $this->stockStatusModel->updateModifiedProductStatus($message);
//        $this->stockAvailabilityModel->processMessage($message);

        $this->logger->info(__('%1 => %2 RabbitMQ for Stock update is done:',
            __CLASS__,
            __FUNCTION__
        ));
    }

}
