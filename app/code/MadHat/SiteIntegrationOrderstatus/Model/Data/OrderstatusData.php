<?php

namespace MadHat\SiteIntegrationOrderstatus\Model\Data;

use MadHat\SiteIntegrationOrderstatus\Api\Data\OrderstatusDataInterface;
use Magento\Framework\Api\AbstractSimpleObject;

class OrderstatusData extends AbstractSimpleObject implements OrderstatusDataInterface
{
    public function getVismaOrderNo()
    {
        return $this->_get(self::VISMA_ORDER_NO);
    }

    public function setVismaOrderNo(?int $vismaOrderNo)
    {
        return $this->setData(self::VISMA_ORDER_NO, $vismaOrderNo);
    }

    public function getWebOrderNo()
    {
        return $this->_get(self::WEB_ORDER_NO);
    }

    public function setWebOrderNo(?string $webOrderNo)
    {
        return $this->setData(self::WEB_ORDER_NO, $webOrderNo);
    }

    public function getExternalOrderNo()
    {
        return $this->_get(self::EXTERNAL_ORDER_NO);
    }

    public function setExternalOrderNo(?string $externalOrderNo)
    {
        return $this->setData(self::EXTERNAL_ORDER_NO, $externalOrderNo);
    }

    public function getPortal()
    {
        return $this->_get(self::PORTAL);
    }

    public function setPortal(?int $portal)
    {
        return $this->setData(self::PORTAL, $portal);
    }

    public function getStatus()
    {
        return $this->_get(self::STATUS);
    }

    public function setStatus(?int $status)
    {
        return $this->setData(self::STATUS, $status);
    }

    public function getStatusText()
    {
        return $this->_get(self::STATUS_TEXT);
    }

    public function setStatusText(?string $statusText)
    {
        return $this->setData(self::STATUS_TEXT, $statusText);
    }

    public function getOrderType()
    {
        return $this->_get(self::ORDER_TYPE);
    }

    public function setOrderType(?int $orderType)
    {
        return $this->setData(self::ORDER_TYPE, $orderType);
    }

    public function getOrderTypeText()
    {
        return $this->_get(self::ORDER_TYPE_TEXT);
    }

    public function setOrderTypeText(?string $orderTypeText)
    {
        return $this->setData(self::ORDER_TYPE_TEXT, $orderTypeText);
    }

    public function getAmountGross()
    {
        return $this->_get(self::AMOUNT_GROSS);
    }

    public function setAmountGross(?float $amountGross)
    {
        return $this->setData(self::AMOUNT_GROSS, $amountGross);
    }

    public function getAmountNet()
    {
        return $this->_get(self::AMOUNT_NET);
    }

    public function setAmountNet(?float $amountNet)
    {
        return $this->setData(self::AMOUNT_NET, $amountNet);
    }

    public function getCostPriceSumNet()
    {
        return $this->_get(self::COST_PRICE_SUM_NET);
    }

    public function setCostPriceSumNet(?float $costPriceSumNet)
    {
        return $this->setData(self::COST_PRICE_SUM_NET, $costPriceSumNet);
    }

    public function getSeller()
    {
        return $this->_get(self::SELLER);
    }

    public function setSeller(?int $seller)
    {
        return $this->setData(self::SELLER, $seller);
    }

    public function getOrderDate()
    {
        return $this->_get(self::ORDER_DATE);
    }

    public function setOrderDate(?string $orderDate)
    {
        return $this->setData(self::ORDER_DATE, $orderDate);
    }

    public function getChangeDate()
    {
        return $this->_get(self::CHANGE_DATE);
    }

    public function setChangeDate(?int $changeDate)
    {
        return $this->setData(self::CHANGE_DATE, $changeDate);
    }

    public function getPaymentMethod()
    {
        return $this->_get(self::PAYMENT_METHOD);
    }

    public function setPaymentMethod(?\MadHat\SiteIntegrationOrderstatus\Api\Data\PaymentMethodInterface $paymentMethod)
    {
        return $this->setData(self::PAYMENT_METHOD, $paymentMethod);
    }

    public function getCurrencyISO()
    {
        return $this->_get(self::CURRENCY_ISO);
    }

    public function setCurrencyISO(?string $currencyISO)
    {
        return $this->setData(self::CURRENCY_ISO, $currencyISO);
    }

    public function getCountryISO()
    {
        return $this->_get(self::COUNTRY_ISO);
    }

    public function setCountryISO(?string $countryISO)
    {
        return $this->setData(self::COUNTRY_ISO, $countryISO);
    }

    public function getShippingCountryISO()
    {
        return $this->_get(self::SHIPPING_COUNTRY_ISO);
    }

    public function setShippingCountryISO(?string $shippingCountryISO)
    {
        return $this->setData(self::SHIPPING_COUNTRY_ISO, $shippingCountryISO);
    }

    public function getShipmentStatus()
    {
        return $this->_get(self::SHIPMENT_STATUS);
    }

    public function setShipmentStatus(?int $shipmentStatus)
    {
        return $this->setData(self::SHIPMENT_STATUS, $shipmentStatus);
    }

    public function getFulfilmentWorkflow()
    {
        return $this->_get(self::FULFILMENT_WORKFLOW);
    }

    public function setFulfilmentWorkflow(?string $fulfilmentWorkflow)
    {
        return $this->setData(self::FULFILMENT_WORKFLOW, $fulfilmentWorkflow);
    }

    public function getOrderRowsData()
    {
        return $this->_get(self::ORDER_ROWS_DATA);
    }

    public function setOrderRowsData(?array $orderRowsData)
    {
        return $this->setData(self::ORDER_ROWS_DATA, $orderRowsData);
    }

    public function getConsignments()
    {
        return $this->_get(self::CONSIGNMENTS);
    }

    public function setConsignments(?array $consignments)
    {
        return $this->setData(self::CONSIGNMENTS, $consignments);
    }

    public function getProductShipments()
    {
        return $this->_get(self::PRODUCT_SHIPMENTS);
    }

    public function setProductShipments(?array $productShipments)
    {
        return $this->setData(self::PRODUCT_SHIPMENTS, $productShipments);
    }

    public function getBillings()
    {
        return $this->_get(self::BILLINGS);
    }

    public function setBillings(?array $billings)
    {
        return $this->setData(self::BILLINGS, $billings);
    }
}