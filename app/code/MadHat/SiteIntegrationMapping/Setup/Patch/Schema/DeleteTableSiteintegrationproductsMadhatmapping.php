<?php

namespace MadHat\SiteIntegrationMapping\Setup\Patch\Schema;

use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\Setup\Patch\PatchInterface;
use Magento\Framework\Setup\Patch\SchemaPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
class DeleteTableSiteintegrationproductsMadhatmapping implements SchemaPatchInterface
{
    /** @var SchemaSetupInterface */
    private $schemaSetup;

    public function __construct(SchemaSetupInterface $schemaSetup)
    {
        $this->schemaSetup = $schemaSetup;
    }

    public function apply()
    {
        $this->schemaSetup->startSetup();

        if ($this->schemaSetup->tableExists('madhat_siteintegrationproducts_madhatmapping')) {
            $this->schemaSetup->getConnection()->dropTable('madhat_siteintegrationproducts_madhatmapping');
        }

        $this->schemaSetup->endSetup();
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }
}
