<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace MadHat\SiteIntegrationMapping\Model;

use MadHat\SiteIntegrationMapping\Api\Data\MappingInterface;
use Magento\Framework\Model\AbstractModel;

class Mapping extends AbstractModel implements MappingInterface
{
    /**
     * Get mapping_id
     * @return string|null
     */
    public function getMappingId()
    {
        return $this->_get(self::MAPPING_ID);
    }

    /**
     * @param $mappingId
     * @return MappingInterface|Mapping
     */
    public function setMappingId($mappingId)
    {
        return $this->setData(self::MAPPING_ID, $mappingId);
    }

    /**
     * Get source
     * @return string|null
     */
    public function getSource()
    {
        return $this->_get(self::SOURCE);
    }
    /**
     * Get destination
     * @return string|null
     */
    public function getDestination()
    {
        return $this->_get(self::DESTINATION);
    }
    /**
     * Get type
     * @return string|null
     */
    public function getType()
    {
        return $this->_get(self::TYPE);
    }
    /**
     * Get created_at
     * @return string|null
     */
    public function getCreatedAt()
    {
        return $this->_get(self::CREATED_AT);
    }
    /**
     * Get updated_at
     * @return string|null
     */
    public function getUpdatedAt()
    {
        return $this->_get(self::UPDATED_AT);
    }

    /**
     * Set source
     * @param string $source
     * @return MappingInterface|Mapping
     */
    public function setSource($source)
    {
        return $this->setData(self::SOURCE, $source);
    }
    /**
     * Set destination
     * @param string $destination
     * @return MappingInterface|Mapping
     */
    public function setDestination($destination)
    {
        return $this->setData(self::DESTINATION, $destination);
    }
    /**
     * Set type
     * @param string $type
     * @return MappingInterface|Mapping
     */
    public function setType($type)
    {
        return $this->setData(self::TYPE, $type);
    }
    /**
     * Set created_at
     * @param string $createdAt
     * @return MappingInterface|Mapping
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }
    /**
     * Set updated_at
     * @param string $updatedAt
     * @return MappingInterface|Mapping
     */
    public function setUpdatedAt($updatedAt)
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }
}

