# MadHat InventoryImport

    MadHat_InventoryImport

- [Overview](#markdown-overview)
- [How to install](#markdown-how-to-install)
- [Import using command prompt](#markdown-import-using-command-prompt)
- [Cron Jobs](#markdown-cron-jobs)


## Overview
The MadHat_InventoryImport module is responsible for import inventory from the SITE system.


##  How to install
\* = in production please use the `--keep-generated` option

### Installation Steps

- The module is available in a MadHat GIT repository for:
    - private github repository
- Enable the module by running `php bin/magento module:enable MadHat_InventoryImport`
- apply database updates by running `php bin/magento setup:upgrade`\*
- Flush the cache by running `php bin/magento cache:flush`


## Import using command prompt

- To import inventory using command line, run below command line:

####Import all the Magento product Inventory from SITE
```
php bin/magento import:inventory --mode="all"
```

####Import update product Inventory from SITE
```
php bin/magento import:inventory
```

## Cron Job Scheduling

The cron is schedule whenever the Magento default cron is executed

```
* * * * *
```
