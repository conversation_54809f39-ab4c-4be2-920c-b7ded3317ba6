<?php

namespace MadHat\InventoryImport\Helper;

use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\ProductRepository;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use MadHat\InventoryImport\Model\Source\ProductStatus;
use MadHat\InventoryImport\Setup\Patch\Data\AddProductStatusAttribute;

class ProductStatusConfig extends \Magento\Framework\App\Helper\AbstractHelper
{
    const XML_PATH_SHOW_PRODUCT_BASED_ON_STATUS = 'cataloginventory/options/on_product_stock_status';
    const XML_MANAGE_STOCK = 'cataloginventory/item_options/manage_stock';
    const XML_IGNORE_RESERVATION = 'cataloginventory/options/ingore_reservation';
    const PRODUCT_STATUS_ATTRIBUTE_TABLE = 'catalog_product_entity_int';
    const PRODUCT_PRICE_ATTRIBUTE_TABLE = 'catalog_product_entity_decimal';
    const PRODUCT_TABLE = 'catalog_product_entity';
    const MIN_SALE_ENABLE = 'cataloginventory/options/enable_min_qty';
    const MIN_SALE_CONFIG_QTY = 'cataloginventory/options/minimum_qty';

    /**
     * @var ProductRepository
     */
    protected $_productRepository;
    /**
     * @var StoreManagerInterface
     */
    protected $_storeManager;
    /**
     * @var ResourceConnection
     */
    protected $_resourceConnection;

    protected $_store;
    protected $_storeId;
    protected $_inventoryStatusAttrId;

    public function __construct(
        Context $context,
        ProductRepository $productRepository,
        StoreManagerInterface $storeManager,
        ResourceConnection $resourceConnection
    ) {
        parent::__construct($context);
        $this->_productRepository = $productRepository;
        $this->_storeManager = $storeManager;
        $this->_resourceConnection = $resourceConnection;
    }

    public function isInventoryOnStatus($scopeCode = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_SHOW_PRODUCT_BASED_ON_STATUS,
            ScopeInterface::SCOPE_WEBSITE,
            $scopeCode
        );
    }
    public function ignoreReservation()
    {
        return $this->scopeConfig->getValue(self::XML_IGNORE_RESERVATION);
    }
    public function isNotManageStock($scopeCode = null)
    {
        return !$this->scopeConfig->getValue(
            self::XML_MANAGE_STOCK,
            ScopeInterface::SCOPE_WEBSITE,
            $scopeCode
        );
    }

    /**
     * @param $product
     * @return bool
     */
    public function isInStock($product)
    {
        $inStock = false;

        $stockStatus = $product->getMadhatInventoryStatus();
        if (empty($stockStatus)) {
            $stockStatus = $this->getInventoryStatus($product->getId(), $product->getStoreId());
        }
        switch ($stockStatus) {
            case ProductStatus::VALUE_IN_STOCK:
            case ProductStatus::VALUE_AVAILABLE_ON_DEMAND:
            case ProductStatus::VALUE_AVAILABLE_ON_PREORDER:
            case ProductStatus::VALUE_ETA_2_DAYS:
            case ProductStatus::VALUE_ETA_3_DAYS:
            case ProductStatus::VALUE_ETA_7_DAYS:
            case ProductStatus::VALUE_ETA_14_DAYS:
            case ProductStatus::VALUE_ETA_30_DAYS:
            //case ProductStatus::VALUE_EXPIRES:
                $inStock = true;
                break;
        }
        return $inStock;
}

    public function getInventoryStatus($productId, $storeId = null)
    {
        $value = false;
        try {
            if (empty($storeId)) {
                $storeId = $this->getStoreId();
            }
            $connection = $this->_resourceConnection->getConnection();
            if (!$this->_inventoryStatusAttrId) {
                $this->_inventoryStatusAttrId = $this->getAttributeId(AddProductStatusAttribute::PRODUCT_INVENTORY_STATUS);
            }
            // check attribute value exists
            $selectQuery = 'select value from ' . self::PRODUCT_STATUS_ATTRIBUTE_TABLE . ' where entity_id = "' . $productId . '" and attribute_id = "' . $this->_inventoryStatusAttrId . '" and store_id = "' . $storeId . '"';
            $value = $connection->fetchOne($selectQuery);
        } catch (\Exception $ex) {
            $this->_logger->error('Error getInventoryStatus : ' . $ex->getMessage());
        }
        return $value;
    }

    /**
     * @return int
     * @throws NoSuchEntityException
     */
    public function getStoreId()
    {
        if (!$this->_storeId) {
            $this->_storeId = $this->getStore()->getId();
        }
        return $this->_storeId;
    }

    /**
     * @return \Magento\Store\Api\Data\StoreInterface
     * @throws NoSuchEntityException
     */
    public function getStore()
    {
        if (!$this->_store) {
            $this->_store = $this->_storeManager->getStore();
        }
        return $this->_store;
    }

    public function getAttributeId($attribute_code)
    {

        // define variable
        $attributeId = '';
        try {
            // create connection
            $connection = $this->_resourceConnection->getConnection();
            $entityTypeQuery = 'select entity_type_id from eav_entity_type where entity_type_code ="' . Product::ENTITY . '"';
            $entityTypeId = $connection->fetchOne($entityTypeQuery);

            // get attribute id by attribute code
            $selectQuery = 'select attribute_id from eav_attribute where attribute_code = "' . $attribute_code . '" and entity_type_id = "' . $entityTypeId . '"';
            $attributeId = $connection->fetchOne($selectQuery);
        } catch (\Exception $e) {
            $this->_logger->error($e->getMessage());
        }

        return $attributeId;
    }

    public function getProductId($sku)
    {
        $value = "";
        try {
            $connection = $this->_resourceConnection->getConnection();
            $selectQuery = 'select entity_id from ' . self::PRODUCT_TABLE . ' where sku = "' . $sku . '"';
            $value = $connection->fetchOne($selectQuery);
        } catch (\Exception $ex) {
            $this->_logger->error('Error getProductId : ' . $ex->getMessage());
        }
        return $value;
    }

    public function isEnabled($product)
    {
        $isEnabled = false;
        $stockStatus = $product->getProductInventoryStatus();
        if (empty($stockStatus)) {
            $product = $this->_productRepository->get($product->getSku(), false, $product->getStoreId());
            $stockStatus = $product->getProductInventoryStatus();
        }
        switch ($stockStatus) {
            case ProductStatus::VALUE_IN_STOCK:
            //case ProductStatus::VALUE_EXPIRES:
            case ProductStatus::VALUE_OUT_OF_STOCK:
            case ProductStatus::VALUE_SOON_IN_STOCK:
            //case ProductStatus::VALUE_DISCONTINUED:
            case ProductStatus::VALUE_AVAILABLE_ON_DEMAND:
            case ProductStatus::VALUE_AVAILABLE_ON_PREORDER:
            case ProductStatus::VALUE_ETA_2_DAYS:
            case ProductStatus::VALUE_ETA_3_DAYS:
            case ProductStatus::VALUE_ETA_7_DAYS:
            case ProductStatus::VALUE_ETA_14_DAYS:
            case ProductStatus::VALUE_ETA_30_DAYS:
                $isEnabled = true;
                break;
        }
        return $isEnabled;
    }

    /**
     * @param $sku
     * @param $storeId
     * @return \Magento\Catalog\Api\Data\ProductInterface|Product|null
     * @throws NoSuchEntityException
     */
    public function getProductBySku($sku, $storeId = null)
    {
        if (empty($storeId)) {
            $storeId = $this->getStoreId();
        }
        return $this->_productRepository->get($sku, false, $storeId);
    }

    /**
     * @param $productId
     * @param $storeId
     * @return \Magento\Catalog\Api\Data\ProductInterface|mixed|null
     * @throws NoSuchEntityException
     */
    public function getProductById($productId, $storeId = null)
    {
        return $this->_productRepository->getById($productId, false, $storeId);
    }

    /**
     * Returns whether the MinQty Feature Enable.
     * @param $scopeCode
     * @return bool
     */
    public function isMinQtyEnable($scopeCode = null): bool
    {
        return $this->scopeConfig->getValue(
            self::MIN_SALE_ENABLE,
            ScopeInterface::SCOPE_WEBSITE,
            $scopeCode
        );
    }

    /**
     * Returns Minimum Sale Config Qty
     * @param $scopeCode
     * @return int
     */
    public function getMinSaleConfigQty($scopeCode = null): int
    {
        return $this->scopeConfig->getValue(
            self::MIN_SALE_CONFIG_QTY,
            ScopeInterface::SCOPE_WEBSITE,
            $scopeCode
        );
    }
}
