<?php

namespace MadHat\InventoryImport\Model;

use Magento\Catalog\Model\Product;
use Magento\Framework\App\ResourceConnection;
use MadHat\InventoryImport\Helper\ProductStatusConfig;
use MadHat\InventoryImport\Model\Logger\ProductStatusLogger;
use MadHat\InventoryImport\Model\Source\ProductStatus;
use MadHat\InventoryImport\Setup\Patch\Data\AddProductStatusAttribute;

abstract class AbstractStockStatus
{
    protected $productInventoryStatusAttributeId;
    protected $productStatusAttributeId;
    protected $productPriceAttributeId;
    protected $_status;
    protected $_qty;
    /** @var ProductStatus */
    protected $_statusMapping;
    /**
     * @var ProductStatusLogger
     */
    protected $_logger;
    /**
     * @var ResourceConnection
     */
    protected $_resourceConnection;

    public function __construct(
        ProductStatus $productStatus,
        ProductStatusLogger $productStatusLogger,
        ResourceConnection $resourceConnection
    ) {
        $this->_status = false;
        $this->_qty = 0;
        $this->productInventoryStatusAttributeId = null;
        $this->productStatusAttributeId = null;
        $this->productPriceAttributeId = null;
        $this->_statusMapping  = $productStatus;
        $this->_logger = $productStatusLogger;
        $this->_resourceConnection = $resourceConnection;
    }

    /**
     * @param $productNo
     * @return bool
     * @throws \Exception
     */

    public function isBaseProduct($productNo): bool
    {
        $matchedTheBase = strtolower(substr(trim($productNo),0,5));
        if ($matchedTheBase === 'base-') {
            return true;
        }
        return false;
    }
    /**
     * @param $availabilityData
     * @return void
     * @throws \Exception
     */

    public function processApiResponse($stockData): void
    {
        $this->_status = false;
        $this->_qty = 0;
        if ($stockData->getProductStatus() !== null && $stockData->getWarehouseAvailabilityStatus() !== null ) {
            $this->_qty = $stockData->getUnitsInStock();
            if ($this->isBaseProduct($stockData->getProductNo())) {
                $this->setStatus("In Stock");
            } else {
                switch ((int) $stockData->getProductStatus()) {
                    case 0:
                        switch ((int) $stockData->getWarehouseAvailabilityStatus()) {
                            case 0:
                            case 710:
                            case 720:
                            case 730:
                            case 740:
                            case 750:
                            case 760:
                            case 770:
                            case 780:
                                $this->setStatus("In Stock");
                                break;
                            case 8:
                                $this->setStatus("Available On Demand"); // yellow color dot + add to cart
                                break;
                            case 9:
                                $this->setStatus("Available On Pre Order"); // yellow color dot + add to cart
                                break;
                            case 761:
                                $this->setStatus("ETA 1-2 Days"); // yellow color dot + add to cart
                            case 781:
                                $this->setStatus("ETA 1-3 Days"); // yellow color dot + add to cart
                                break;
                            case 2:
                            case 711:
                                $this->setStatus("ETA 7 Days"); // yellow color dot + add to cart
                                break;
                            case 4:
                            case 721:
                                $this->setStatus("ETA 14 Days"); // yellow color dot + add to cart
                                break;
                            case 5:
                            case 751:
                                $this->setStatus("ETA 30 Days"); // yellow color dot + add to cart
                                break;
                            case 6:
                            case 736:
                            case 7:
                            case 741:
                            case 66:
                            case 776:
                                $this->setStatus("Out of Stock");
                                break;
                            default:
                                $this->setStatus("Discontinued");
                        }
                        break;
                    case 256:
                        switch ((int) $stockData->getWarehouseAvailabilityStatus()) {
                            case 0:
                            case 710:
                            case 720:
                            case 730:
                            case 740:
                            case 750:
                            case 760:
                            case 770:
                            case 780:
                                $this->setStatus("In Stock");
                                break;
                            case 2:
                            case 711:
                                //    $this->setStatus("Soon in Stock");
                                //    break;
                            case 6:
                            case 736:
                            case 4:
                            case 721:
                            case 5:
                            case 781:
                            case 7:
                            case 741:
                            case 66:
                            case 776:
                            case 9:
                            case 8:
                            case 761:
                            case 751:
                                //$this->setStatus("Out of Stock");
                                $this->setStatus("Discontinued");
                                break;
                            default:
                                $this->setStatus("Discontinued");
                        }
                        break;
                    case 1:
                        $this->setStatus("Discontinued");
                        break;
                    case 8:
                    case 4096:
                        //$this->setStatus("Not Released");
                        $this->setStatus("Discontinued");
                        break;
                    default:
                        $this->setStatus("Discontinued");
                }
            }
        }
    }

    /*
    public function processApiResponseOld($stockData): void
    {
        $this->_status = false;
        $this->_qty = 0;
        if ($stockData->getProductStatus() && $stockData->getWarehouseAvailabilityStatus()) {
            $this->_qty = $stockData->getUnitsInStock();
            switch ($stockData->getProductStatus()) {
                case 'Normal':
                case 'UtgangenMedSaldo':
                    switch ($stockData->getWarehouseAvailabilityStatus()) {
                        case 'NormalStatus_0':
                        case 'GreenSymbolShowingInSTock_710':
                        case 'GreenSymbolShowingInSTock_720':
                        case 'GreenSymbolShowingInStock_730':
                        case 'GreenSymbolShowingInStock_740':
                        case 'GreenSymbolShowingInStock_750':
                        case 'GreenSymbolShowingInStock_760':
                        case 'GreenSymbolShowingInStock_770':
                        case 'GreenSymbolShowingInStock_780':
                        case 'UtgangenMedSaldo':
                            $this->setStatus("In Stock");
                            break;
                        case 'TemporarilyProlongedDeliveryTimeUpToOneWeek_2':
                        case 'TemporarilyProlongedDeliveryTimeUpToOneWeek_711':
                            $this->setStatus("Soon in Stock");
                            break;
                        case 'Soldout_6':
                        case 'Soldout_736':
                        case 'TemporarilyProlongedDeliveryTimeUpToTwoWeeks_4':
                        case 'TemporarilyProlongedDeliveryTimeUpToTwoWeeks_721':
                        case 'TemporarilyProlongedDeliveryTimeUpToThreeWeeks_5':
                        case 'TemporarilyProlongedDeliveryTimeUpToThreeWeeks_781':
                        case 'ProlongedDeliveryTimeUncertainDeliveryDate_7':
                        case 'ProlongedDeliveryTimeUncertainDeliveryDate_741':
                        case 'TemporarilyOutOfStock_66':
                        case 'TemporarilyOutOfStock_776':
                        case 'NewProductProlongedDeliveryTime_9':
                        case 'NewProductProlongedDeliveryTime_761':
                        case 'ReservationProduct_751':
                            $this->setStatus("Out of Stock");
                            break;
                        default:
                            $this->setStatus(false);
                    }
                    break;
                case 'Utgangen':
                    $this->setStatus("Discontinued");
                    break;
                case 'Inaktiv':
                case 'EjLanseradArtikel':
                    $this->setStatus("Not Released");
                    break;
                default:
                    $this->setStatus(false);
            }
        }
    }
    */

    public function getStatus()
    {
        return $this->_status;
    }

    protected function setStatus($status)
    {
        $this->_status = $status;
    }

    public function getQty()
    {
        if ($this->_qty < 0) {
            $this->_qty = 1;
        }
        return $this->_qty;
    }

    public function getStatusId()
    {
        return $this->_statusMapping->getOptionId($this->_status);
    }

    public function getStatusLabel($status_id)
    {
        return $this->_statusMapping->getOptionText($status_id);
    }

    /**
     * Function to update attribute value
     */
    public function updateStatus($productId, $inventoryAttributeValue, $storeCodeId, $productNo)
    {
        try {
            if (!$this->productInventoryStatusAttributeId) {
                // get attribute id by attribute code
                $this->productInventoryStatusAttributeId = $this->getAttributeId(AddProductStatusAttribute::PRODUCT_INVENTORY_STATUS);
            }
            if (!$this->productStatusAttributeId) {
                $this->productStatusAttributeId = $this->getAttributeId(Product::STATUS);
            }
            if (!$this->productPriceAttributeId) {
                $this->productPriceAttributeId = $this->getAttributeId(Product::PRICE);
            }

            if ($this->productInventoryStatusAttributeId != '') {
                $attributeId = $this->productInventoryStatusAttributeId;
                // create connection
                $connection = $this->_resourceConnection->getConnection();
                // check attribute value exists
                $selectQuery = 'select value_id, value from '.ProductStatusConfig::PRODUCT_STATUS_ATTRIBUTE_TABLE.' where entity_id = "'.$productId.'" and attribute_id = "'.$attributeId.'" and store_id = "'.$storeCodeId.'"';
                $value = $connection->fetchRow($selectQuery);

                if ($value) {
                    if (empty($value['value']) || $inventoryAttributeValue != $value['value']) {
                        $valueId = $value['value_id'];
                        // update
                        $updateQuery = 'update ' . ProductStatusConfig::PRODUCT_STATUS_ATTRIBUTE_TABLE . ' set value = "' . $inventoryAttributeValue . '" where entity_id = "' . $productId . '" and attribute_id = "' . $attributeId . '" and value_id = "' . $valueId . '"';
                        $connection->query($updateQuery);
                    } else {
                        $this->_logger->info('No change in Inventory status');
                    }
                } else {
                    // insert
                    $insertQuery = 'insert into '.ProductStatusConfig::PRODUCT_STATUS_ATTRIBUTE_TABLE.' (attribute_id, store_id, entity_id, value) Values ("'.$attributeId.'","'.$storeCodeId.'","'.$productId.'","'.$inventoryAttributeValue.'")';
                    $connection->query($insertQuery);
                }
            }

            $this->updateEnableStatus($storeCodeId, $productId, $inventoryAttributeValue, $productNo);
        } catch (\Exception $e) {
            $this->writeExceptionLog('Update Error : productId : '.$productId.' storeCodeId : '.$storeCodeId.' Error : '.$e->getMessage());
        }
    }

    public function getAttributeId($attribute_code)
    {

        // define variable
        $attributeId = '';
        try {
            // create connection
            $connection = $this->_resourceConnection->getConnection();
            $entityTypeQuery = 'select entity_type_id from eav_entity_type where entity_type_code ="' . Product::ENTITY . '"';
            $entityTypeId = $connection->fetchOne($entityTypeQuery);

            // get attribute id by attribute code
            $selectQuery = 'select attribute_id from eav_attribute where attribute_code = "' . $attribute_code . '" and entity_type_id = "' . $entityTypeId . '"';
            $attributeId = $connection->fetchOne($selectQuery);
        } catch (\Exception $e) {
            $this->_logger->error($e->getMessage());
        }

        return $attributeId;
    }

    public function updateEnableStatus($storeCodeId, $productId, $inventoryAttributeValue, $productNo)
    {
        /* enable/disable product based on status */
        if ($this->productStatusAttributeId != '') {

            $attributeStatusId = $this->productStatusAttributeId;

            $statusValue = Product\Attribute\Source\Status::STATUS_DISABLED; //disabled
            $productPrice = false;
            if ($this->isProductEnabled($inventoryAttributeValue)) {
                $productPrice = $this->getProductPrice($storeCodeId, $productId);
                if ($productPrice > 0 && (
                    $inventoryAttributeValue != ProductStatus::VALUE_DISCONTINUED ||
                    $inventoryAttributeValue != ProductStatus::VALUE_EXPIRES ||
                    $inventoryAttributeValue != ProductStatus::VALUE_NOT_RELEASED))
                {
                    $statusValue = Product\Attribute\Source\Status::STATUS_ENABLED;
                    //enabled product if price not 0 && Not DISCONTINUED || Not EXPIRES || Not NOT_RELEASED
                }
            }

            //enable Base Product
            if ($this->isBaseProduct($productNo)) {
                $statusValue = Product\Attribute\Source\Status::STATUS_ENABLED;
            }

            $this->_logger->info('Price : '.$productPrice.' Status : '.$statusValue.' Store Id: '.$storeCodeId);
            // create connection
            $connection = $this->_resourceConnection->getConnection();
            // check attribute value exists
            $selectQuery = 'select value_id, value from '.ProductStatusConfig::PRODUCT_STATUS_ATTRIBUTE_TABLE.' where entity_id = "'.$productId.'" and attribute_id = "'.$attributeStatusId.'" and store_id = "'.$storeCodeId.'"';
            $value = $connection->fetchRow($selectQuery);

            if ($value) {
                if (empty($value['value']) || $statusValue != $value['value']) {
                    $valueId = $value['value_id'];
                    // update
                    $updateQuery = 'update ' . ProductStatusConfig::PRODUCT_STATUS_ATTRIBUTE_TABLE . ' set value = "' . $statusValue . '" where entity_id = "' . $productId . '" and attribute_id = "' . $attributeStatusId . '" and value_id = "' . $valueId . '"';
                    $connection->query($updateQuery);
                } else {
                    $this->_logger->info('No change in Product Status');
                }
            } else {
                // insert
                $insertQuery = 'insert into '.ProductStatusConfig::PRODUCT_STATUS_ATTRIBUTE_TABLE.' (attribute_id, store_id, entity_id, value) Values ("'.$attributeStatusId.'","'.$storeCodeId.'","'.$productId.'","'.$statusValue.'")';
                $connection->query($insertQuery);
            }
        }
    }
    public function getProductPrice($storeCodeId, $productId)
    {
        $productPrice = 0;
        if ($this->productPriceAttributeId) {
            // create connection
            $connection = $this->_resourceConnection->getConnection();
            // check attribute value exists
            $selectQuery = 'select value from '.ProductStatusConfig::PRODUCT_PRICE_ATTRIBUTE_TABLE.' where entity_id = "'.$productId.'" and attribute_id = "'.$this->productPriceAttributeId.'" and store_id = "'.$storeCodeId.'"';
            $productPrice = $connection->fetchOne($selectQuery);
        }
        return $productPrice;
    }
    public function isProductEnabled($inventoryStatusValue)
    {
        $isEnabled = false;
        switch ($inventoryStatusValue) {
            case ProductStatus::VALUE_IN_STOCK:
            //case ProductStatus::VALUE_EXPIRES:
            case ProductStatus::VALUE_OUT_OF_STOCK:
            case ProductStatus::VALUE_SOON_IN_STOCK:
            //case ProductStatus::VALUE_DISCONTINUED:
            case ProductStatus::VALUE_AVAILABLE_ON_DEMAND:
            case ProductStatus::VALUE_AVAILABLE_ON_PREORDER:
            case ProductStatus::VALUE_ETA_2_DAYS:
            case ProductStatus::VALUE_ETA_3_DAYS:
            case ProductStatus::VALUE_ETA_7_DAYS:
            case ProductStatus::VALUE_ETA_14_DAYS:
            case ProductStatus::VALUE_ETA_30_DAYS:
                $isEnabled = true;
                break;
        }
        return $isEnabled;
    }

    public function writeLog($message)
    {
        $this->_logger->info($message);
    }
    public function writeExceptionLog($message)
    {
        $this->_logger->error($message);
    }

}
