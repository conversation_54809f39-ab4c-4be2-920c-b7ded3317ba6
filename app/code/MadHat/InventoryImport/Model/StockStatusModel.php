<?php

namespace MadHat\InventoryImport\Model;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\ProductRepository;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\Config\Storage\WriterInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\InventoryApi\Api\Data\SourceItemInterface;
use Magento\InventoryApi\Api\SourceItemsSaveInterface;
use Magento\InventoryApi\Api\Data\SourceItemInterfaceFactory;
use Magento\InventorySalesApi\Model\GetAssignedStockIdForWebsiteInterface;
use Magento\Store\Api\StoreRepositoryInterface;
use Magento\Store\Api\WebsiteRepositoryInterface;
use Magento\Store\Model\Store;
use Magento\Store\Model\StoreManagerInterface;
use MadHat\InventoryImport\Model\Logger\ProductStatusLogger;
use MadHat\InventoryImport\Model\Source\ProductStatus;
use MadHat\InventoryImport\Helper\ProductStatusConfig;
use MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider;
use MadHat\DbLogger\Logger\DbLoggerSaver;
use Magento\Store\Model\App\Emulation;

use Magento\InventoryCatalog\Model\GetSourceItemsBySkuAndSourceCodes;
use Magento\InventorySales\Model\GetProductSalableQty;

use MadHat\InventoryImport\Helper\ManageStockStatus;

class StockStatusModel extends AbstractStockStatus
{
    const INVENTORY_DEFAULT_MAPPING = 'inventory_import/settings/inventory_mapping';
    const WAREHOUSE_NUMBER = 'inventory_import/settings/warehouse';
    const WAREHOUSE_NUMBER_DEFAULT = '1';

    protected $_scopeConfig;
    protected $websiteRepository;
    protected $storeRepository;
    protected $configWriter;
    protected $_productRepository;
    protected $storeManager;
    protected $getAssignedStockId;
    protected $sourceItemsSaveInterface;
    protected $sourceItemFactory;
    /**
     * @var DbLoggerSaver
     */
    protected DbLoggerSaver $dbLoggerSaver;

    /**
     * @var Emulation
     */
    protected Emulation $emulation;

    /**
     * @var GetSourceItemsBySkuAndSourceCodes
     */
    protected $getSourceItemsBySkuAndSourceCodes;

    /**
     * @var GetProductSalableQty
     */
    protected $getProductSalableQty;

    /**
     * @var ManageStockStatus
     */
    protected $manageStockStatus;

    /**
     * @param ProductStatus $productStatus
     * @param ProductStatusLogger $productStatusLogger
     * @param ResourceConnection $resourceConnection
     * @param ScopeConfigInterface $scopeConfig
     * @param WebsiteRepositoryInterface $websiteRepository
     * @param StoreRepositoryInterface $storeRepository
     * @param WriterInterface $configWriter
     * @param ProductRepository $productRepository
     * @param StoreManagerInterface $storeManager
     * @param GetAssignedStockIdForWebsiteInterface $getAssignedStockId
     * @param SourceItemsSaveInterface $sourceItemsSaveInterface
     * @param SourceItemInterfaceFactory $sourceItemFactory
     * @param DbLoggerSaver $dbLoggerSaver
     * @param ManageStockStatus $manageStockStatus
     * @param Emulation $emulation
     */
    public function __construct(
        ProductStatus $productStatus,
        ProductStatusLogger $productStatusLogger,
        ResourceConnection $resourceConnection,
        ScopeConfigInterface $scopeConfig,
        WebsiteRepositoryInterface $websiteRepository,
        StoreRepositoryInterface $storeRepository,
        WriterInterface $configWriter,
        ProductRepository $productRepository,
        StoreManagerInterface $storeManager,
        GetAssignedStockIdForWebsiteInterface $getAssignedStockId,
        SourceItemsSaveInterface $sourceItemsSaveInterface,
        SourceItemInterfaceFactory $sourceItemFactory,
        DbLoggerSaver $dbLoggerSaver,
        Emulation $emulation,
        GetSourceItemsBySkuAndSourceCodes $getSourceItemsBySkuAndSourceCodes,
        GetProductSalableQty $getProductSalableQty,
        ManageStockStatus $manageStockStatus
    ) {
        parent::__construct(
            $productStatus,
            $productStatusLogger,
            $resourceConnection
        );
        $this->_scopeConfig = $scopeConfig;
        $this->websiteRepository = $websiteRepository;
        $this->storeRepository = $storeRepository;
        $this->configWriter = $configWriter;
        $this->_productRepository = $productRepository;
        $this->storeManager = $storeManager;
        $this->getAssignedStockId = $getAssignedStockId;
        $this->sourceItemsSaveInterface = $sourceItemsSaveInterface;
        $this->sourceItemFactory = $sourceItemFactory;
        $this->dbLoggerSaver = $dbLoggerSaver;
        $this->emulation = $emulation;
        $this->getSourceItemsBySkuAndSourceCodes = $getSourceItemsBySkuAndSourceCodes;
        $this->getProductSalableQty = $getProductSalableQty;
        $this->manageStockStatus = $manageStockStatus;
    }

    public function updateModifiedProductStatus(array $siteRMQData): void
    {
        $this->writeLog("********** Update Modified Products **********");
        $mapping = $this->getDefaultInventoryMapping();
        if (empty($mapping)) {
            $this->writeLog("********** Inventory Mapping Not Valid **********");
            return;
        }

        $this->totalInventory = count($siteRMQData);
        $this->savedInventory = 0;
        $this->failedInventory = 0;
        $this->inventoryImportErrors = [];

        foreach ($mapping as $key => $value) {
            foreach ($value as $inventoryInfo) {
                try {
                    $websiteCode = $inventoryInfo['website'];
                    $storeCode = $inventoryInfo['store'];
                    $sourceCode = $inventoryInfo['source'];
                    $warehouseCode = $inventoryInfo['warehouse'];
                    // get website id
                    $websiteId = $this->getWebsiteIdByCode($websiteCode);
                    //get websites to be updated
                    $stockId = $this->getAssignedStockId->execute($websiteCode);
                    $websiteIdsForStockSource = $this->getWebsitesFromSource($stockId);
                    // get store id
                    $storeCodeId = $this->getStoreIdByCode($storeCode);
                    $params = [];

                    $defineWarehouseId = $this->getWarehouseId('website', $websiteCode);
                    $this->writeLog("defineWarehouseId : " . $defineWarehouseId);
                    if (in_array($warehouseCode, explode(",", $defineWarehouseId))) {
                        $warehouseId = $warehouseCode;
                        $this->writeLog("WarehouseNo : " . $warehouseId);

                        $inventoryItemCount = 0;
                        $inventoryItems = [];
                        $startSku = $endSku = '';
                        foreach ($siteRMQData as $stockData) {
                            $productSku = $stockData->getProductNo();
                            $product = $this->getProductBySku($productSku);
                            if ($product) {
                                $inventoryItemCount++;
                                if ($inventoryItemCount == 1) {
                                    $startSku = $productSku;
                                }
                                $this->writeLog('---------------------------------------------------');
                                $productInWebsites = $product->getWebsiteIds();
                                $this->writeLog('Available in Websites : ' . json_encode($productInWebsites, true));
                                $this->writeLog('SKU : ' . $productSku . ' WebsiteIds : ' . json_encode($websiteIdsForStockSource, true));
                                $this->processApiResponse($stockData);
                                $this->writeLog('Inventory Status : ' . $this->getStatus() . ' Qty :' . $this->getQty());

                                if ($this->getStatus()) {
                                    $this->writeLog("Set Status as : " . $this->getStatus());
                                    //Assign qty to warehouse also fix warehouse assignment

                                    $productStockStatus = false;
                                    $manageStockSetAsNo = false;
                                    if ($this->getStatus() == ProductStatus::LABEL_IN_STOCK ||
                                        $this->getStatus() == ProductStatus::LABEL_AVAILABLE_ON_DEMAND ||
                                        $this->getStatus() == ProductStatus::LABEL_AVAILABLE_ON_PREORDER ||
                                        $this->getStatus() == ProductStatus::LABEL_ETA_2_DAYS ||
                                        $this->getStatus() == ProductStatus::LABEL_ETA_3_DAYS ||
                                        $this->getStatus() == ProductStatus::LABEL_ETA_7_DAYS ||
                                        $this->getStatus() == ProductStatus::LABEL_ETA_14_DAYS ||
                                        $this->getStatus() == ProductStatus::LABEL_ETA_30_DAYS
                                    ) {
                                        $productStockStatus = true;
                                    }
                                    if ($this->getStatus() == ProductStatus::LABEL_AVAILABLE_ON_DEMAND ||
                                        $this->getStatus() == ProductStatus::LABEL_AVAILABLE_ON_PREORDER ||
                                        $this->getStatus() == ProductStatus::LABEL_ETA_2_DAYS ||
                                        $this->getStatus() == ProductStatus::LABEL_ETA_3_DAYS ||
                                        $this->getStatus() == ProductStatus::LABEL_ETA_7_DAYS ||
                                        $this->getStatus() == ProductStatus::LABEL_ETA_14_DAYS ||
                                        $this->getStatus() == ProductStatus::LABEL_ETA_30_DAYS
                                    ) {
                                        $manageStockSetAsNo = true;
                                    }

                                    if ($inventoryItemCount == 20) {
                                        $endSku = $productSku;
                                        $this->writeLog('Updating stock source item from sku : ' . $startSku . ' to sku : ' . $endSku);
                                        $this->updateProductInventory($inventoryItems);
                                        $inventoryItemCount = 0;
                                        $startSku = $endSku = '';
                                        $inventoryItems = [];
                                    } else {
                                        $sourceItem = $this->getSourceItem($productSku, $this->getQty(),
                                            $productStockStatus, $sourceCode, $stockId);
                                        if ($sourceItem) {
                                            array_push($inventoryItems, $sourceItem);
                                        }
                                    }
                                    //END assign qty to warehouse

                                    $statusValue = $this->getStatusId();
                                    foreach ($websiteIdsForStockSource as $websiteIdToUpdate) {
                                        if (in_array($websiteIdToUpdate, $productInWebsites)) {
                                            // if product present in current website id then proceed with update
                                            $this->writeLog('Updating for Website : ' . $websiteIdToUpdate);
                                            foreach ($this->getStoreIdsForWebsite($websiteIdToUpdate) as $storeId) {
                                                $this->updateStatus($product->getId(), $statusValue, Store::DEFAULT_STORE_ID, $productSku);
                                                // new code changes for stock status : set Manage stock No at Product level
                                                if ($manageStockSetAsNo) {
                                                    try {
                                                        $this->writeLog('Updating Manage stock as NO for SKU :: '. $productSku);
                                                        $this->manageStockStatus->updateProductStock($product->getId(), $productSku);
                                                    } catch (\Exception $e) {
                                                        $this->writeLog('Exception Set Manage stock as NO SKU :: '. $productSku);
                                                        $this->writeExceptionLog('Exception Set Manage stock as NO SKU :: '. $productSku);
                                                        $this->writeExceptionLog('Exception Set Manage stock as NO SKU :: '. $productSku.' Error : '.$e->getMessage());
                                                    }
                                                } else {
                                                    try {
                                                        $this->writeLog('Updating Manage stock as YES for SKU :: '. $productSku);
                                                        $this->manageStockStatus->updateProductManageStockYes($product->getId(), $productSku);
                                                    } catch (\Exception $e) {
                                                        $this->writeLog('Exception Set Manage stock as YES SKU :: '. $productSku);
                                                        $this->writeExceptionLog('Exception Set Manage stock as YES SKU :: '. $productSku);
                                                        $this->writeExceptionLog('Exception Set Manage stock as YES SKU :: '. $productSku.' Error : '.$e->getMessage());
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    $this->savedInventory++;
                                }
                            } else {
                                $this->failedInventory++;
                                $this->inventoryImportErrors[] = [
                                    'ProductNo' => $productSku,
                                    'ErrorMessage' => 'Product does not exist'
                                ];
                                $this->writeExceptionLog('Product does not exist in Magento with SKU: ' . $productSku);
                            }
                        }
                        if ($inventoryItemCount > 0) {
                            $endSku = $productSku;
                            $this->writeLog('Updating stock source item from sku : ' . $startSku . ' to sku : ' . $endSku);
                            $this->updateProductInventory($inventoryItems);
                            $inventoryItems = [];
                        }
                    }
                } catch (\Exception $ex) {
                    $this->writeExceptionLog($ex->getMessage());
                }
            }
        }

        $this->addDbLoggerStockRecord();

        $this->writeLog("********** Completed Update Modified Products **********");
    }

    /**
     * Function to get default inventory mapping from configuration
     */
    public function getDefaultInventoryMapping()
    {
        $mappingJsonDecode = [];
        try {
            // get config data
            //$mappingJson = $this->_scopeConfig->getValue(self::INVENTORY_DEFAULT_MAPPING);
            $mappingJson = '{"stockid_1":[{"website":"base","store":"default","source":"default","warehouse":1}]}';
            // decode data
            if (isset($mappingJson)) {
                $mappingJsonDecode = json_decode($mappingJson, true);
                if (count($mappingJsonDecode) > 0) {
                    return $mappingJsonDecode;
                }
            }
        } catch (\Exception $e) {
            $this->writeExceptionLog($e->getMessage());
        }

        return $mappingJsonDecode;
    }

    /**
     * Function to get website id
     */
    public function getWebsiteIdByCode($websiteCode): ?int
    {
        $websiteId = null;
        try {
            $website = $this->websiteRepository->get($websiteCode);
            $websiteId = (int)$website->getId();
        } catch (\Exception $e) {
            $this->writeExceptionLog($e->getMessage());
        }

        return $websiteId;
    }

    public function getWebsitesFromSource($stockId): array
    {
        $websiteIds = [];
        $connection = $this->_resourceConnection->getConnection();
        $selectQuery = 'SELECT * FROM `inventory_stock_sales_channel` WHERE `stock_id` = ' . $stockId;
        $salesChannels = $connection->fetchAll($selectQuery);
        foreach ($salesChannels as $salesChannel) {
            $websiteId = $this->getWebsiteIdByCode($salesChannel['code']);
            //array_push($websiteIds, $websiteId);
            $websiteIds[] = $websiteId;
        }
        return $websiteIds;
    }

    /**
     * Function to get store id
     */
    public function getStoreIdByCode($storeCode): ?int
    {
        $storeId = null;
        try {
            $store = $this->storeRepository->get($storeCode);
            $storeId = (int)$store->getId();
        } catch (\Exception $e) {
            $this->writeExceptionLog($e->getMessage());
        }

        return $storeId;
    }

    public function getWarehouseId($scope = ScopeConfigInterface::SCOPE_TYPE_DEFAULT, $scopeCode = null)
    {
        $warehouseId = $this->_scopeConfig->getValue(
            self::WAREHOUSE_NUMBER,
            $scope,
            $scopeCode
        );

        if (empty($warehouseId)) {
            $warehouseId = self::WAREHOUSE_NUMBER_DEFAULT;
        }

        return $warehouseId;
    }

    /**
     * @param $sku
     * @return false|ProductInterface|Product
     */
    protected function getProductBySku($sku): false|ProductInterface|Product
    {
        $product = false;
        try {
            $product = $this->_productRepository->get($sku);
        } catch (NoSuchEntityException $e) {
            $this->writeExceptionLog($e->getLogMessage());
        }
        return $product;
    }

    /**
     * Function to update product inventory
     */
    public function updateProductInventory($sourceItems): void
    {
        try {
            // update product qty based on source code
            $this->sourceItemsSaveInterface->execute($sourceItems);
        } catch (\Exception $e) {
            $this->inventoryImportErrors[] = [
                'ProductNo' => 'sourceItems issue updateProductInventory',
                'ErrorMessage' => $e->getMessage()
            ];
            $this->writeExceptionLog($e->getMessage());
        }
    }

    /**
     * getSourceItem
     * return false|SourceItemInterface
     */
    public function getSourceItem($sku, $qty, $stockStatus, $sourceCode, $stockId): false|SourceItemInterface
    {
        try {
            // update product qty based on source code
            $sourceItem = $this->sourceItemFactory->create();
            $qty = $this->getProductInventory($sku, $qty, $sourceCode, $stockId);
            $sourceItem->setSourceCode($sourceCode);
            $sourceItem->setSku($sku);
            $sourceItem->setQuantity($qty);
            $sourceItem->setStatus($stockStatus);
            return $sourceItem;
        } catch (\Exception $e) {
            $this->inventoryImportErrors[] = [
                'ProductNo' => $sku,
                'ErrorMessage' => $e->getMessage()
            ];
            $this->writeExceptionLog($e->getMessage());
        }
        return false;
    }

    public function getStoreIdsForWebsite($websiteId): array
    {
        $storeIds = [];
        try {
            $storeIds = $this->storeManager->getStoreByWebsiteId($websiteId);
        } catch (\Exception $ex) {
            $this->writeExceptionLog($ex->getMessage());
        }
        return $storeIds;
    }

    public function addDbLoggerStockRecord(): void
    {
        $message = "Total Inventory : ".$this->totalInventory;
        $message .= " | Saved Inventory : ".$this->savedInventory;
        $message .= " | Failed Inventory : ".$this->failedInventory;

        if (!empty($this->inventoryImportErrors)) {
            $message .= " | Errors : ". json_encode($this->inventoryImportErrors);
        }

        $defaultStoreId = 0;
        $defaultStoreId = $this->storeManager->getDefaultStoreView()->getId();

        if (!$defaultStoreId) {
            $defaultStoreId = 1;
        }

        $this->emulation->startEnvironmentEmulation(1);
        $this->dbLoggerSaver->addRecord(
            'Inventory Import Report',
            $message,
            'NOTICE',
            LogIdentifierProvider::INVENTORY
        );
        $this->emulation->stopEnvironmentEmulation();
    }


    /**
     * Function to get product correct qty for update
     *
     * @param $productSku
     * @param $erpQty
     * @return array
     */
    public function getProductInventory($productSku, $erpQty, $sourceCode, $stockId)
    {
        // define variable
        $finalQty = $erpQty;

        if ($this->_scopeConfig->getValue(ProductStatusConfig::XML_IGNORE_RESERVATION)) {
            return $finalQty;
        }

        try {
            // get product total qty
            $stockItem = $this->getSourceItemsBySkuAndSourceCodes->execute($productSku, [$sourceCode]);
            // check quantity of product
            if (count($stockItem) > 0) {
                foreach ($stockItem as $data) {
                    $totalQty = $data->getQuantity();
                }
            } else {
                $totalQty = 0;
            }

            // get product salable qty
            $salableQty = $this->getProductSalableQty->execute($productSku, $stockId);
            if (!isset($salableQty)) {
                $salableQty = 0;
            }

            // get product reserved qty
            if ($totalQty >= 0 && $totalQty >= $salableQty && $salableQty > 0) {
                $reservedQty = abs($totalQty - $salableQty);
            } else {
                $connection = $this->_resourceConnection->getConnection();
                $selectQuery = 'select sum(quantity) from inventory_reservation where sku = "'.$productSku.'" and stock_id = "'.$stockId.'"';
                $qty = $connection->fetchOne($selectQuery);

                if ($qty < 0) {
                    $reservedQty = abs($qty);
                } else {
                    $reservedQty = 0;
                }

                // log all details
                $this->_logger->info(
                    'Wrong data for SKU = ' . $productSku . ', QTY = '. $qty .', Saleable = '. $salableQty . ', Total QTY = ' . $totalQty
                );
            }

            // check product erp qty
            if ($erpQty <= 0) {
                $erpQty = 0;
            }

            // product final qty
            $finalQty = $erpQty + $reservedQty;

            // log all details
            $this->_logger->info(
                'SKU :: '.$productSku.' :: Before Update Total: ('. $totalQty . ') Salable: (' . $salableQty . ') Reserved: (' . $reservedQty . ') ERP: (' . $erpQty . ') FinalSaleable: (' . $finalQty . ') StockId: ('.$stockId.')'
            );

        } catch (\Exception $e) {
            $this->_logger->error($e->getMessage());
        }

        return $finalQty;
    }
}
