<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Communication/etc/communication.xsd">
    <topic name="site.techoutlet.price" request="MadHat\SiteIntegrationPrice\Api\Data\PriceDataInterface[]">
        <handler name="madhat.siteintegrationprice.consumer"
                 type="MadHat\SiteIntegrationPrice\Model\RabbitMQ\PriceConsumer" method="processMessage"/>
    </topic>

    <!-- For Developer Only -->
<!--    <topic name="site.techoutlet-dev.price.olegh" request="MadHat\SiteIntegrationPrice\Api\Data\PriceDataInterface[]">-->
<!--        <handler name="madhat.siteintegrationprice.consumer"-->
<!--                 type="MadHat\SiteIntegrationPrice\Model\RabbitMQ\PriceConsumer" method="processMessage"/>-->
<!--    </topic>-->
</config>
