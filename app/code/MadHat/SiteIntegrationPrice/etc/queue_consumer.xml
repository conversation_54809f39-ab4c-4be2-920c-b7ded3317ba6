<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
<!--    <consumer name="madhat.siteintegrationprice.consumer" queue="site.techoutlet.price" connection="amqpSITE" handler="MadHat\SiteIntegrationPrice\Model\RabbitMQ\PriceConsumer::processMessage"/>-->

    <!-- For developer only -->
    <consumer name="madhat.siteintegrationprice.consumer" queue="site.techoutlet-dev.price.olegh" connection="amqpSITE" handler="MadHat\SiteIntegrationPrice\Model\RabbitMQ\PriceConsumer::processMessage"/>
</config>
