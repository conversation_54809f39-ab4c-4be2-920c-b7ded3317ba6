<?php

namespace MadHat\SiteIntegrationPrice\Model\Data;

use MadHat\SiteIntegrationPrice\Api\Data\PriceDataInterface;

class PriceData implements PriceDataInterface
{
    protected ?string $productNo;
    protected ?string $externalProductNo;
    protected ?float $priceNet;
    protected ?float $discountPercentage;
    protected ?int $taxClass;
    protected ?string $currencyCode;
    protected ?string $countryCode;
    protected ?int $numberOfUnits;
    protected ?int $portalNumber;
    protected ?int $interval;
    protected ?int $transactionType;
    protected ?int $marketingType;
    protected ?int $intervalInDays;
    protected ?string $dummyValue;

    public function getProductNo(): ?string
    {
        return $this->productNo;
    }

    public function setProductNo(?string $productNo): void
    {
        $this->productNo = $productNo;
    }

    public function getExternalProductNo(): ?string
    {
        return $this->externalProductNo;
    }

    public function setExternalProductNo(?string $externalProductNo): void
    {
        $this->externalProductNo = $externalProductNo;
    }

    public function getPriceNet(): ?float
    {
        return $this->priceNet;
    }

    public function setPriceNet(?float $priceNet): void
    {
        $this->priceNet = $priceNet;
    }

    public function getDiscountPercentage(): ?float
    {
        return $this->discountPercentage;
    }

    public function setDiscountPercentage(?float $discountPercentage): void
    {
        $this->discountPercentage = $discountPercentage;
    }

    public function getTaxClass(): ?int
    {
        return $this->taxClass;
    }

    public function setTaxClass(?int $taxClass): void
    {
        $this->taxClass = $taxClass;
    }

    public function getCurrencyCode(): ?string
    {
        return $this->currencyCode;
    }

    public function setCurrencyCode(?string $currencyCode): void
    {
        $this->currencyCode = $currencyCode;
    }

    public function getCountryCode(): ?string
    {
        return $this->countryCode;
    }

    public function setCountryCode(?string $countryCode): void
    {
        $this->countryCode = $countryCode;
    }

    public function getNumberOfUnits(): ?int
    {
        return $this->numberOfUnits;
    }

    public function setNumberOfUnits(?int $numberOfUnits): void
    {
        $this->numberOfUnits = $numberOfUnits;
    }

    public function getPortalNumber(): ?int
    {
        return $this->portalNumber;
    }

    public function setPortalNumber(?int $portalNumber): void
    {
        $this->portalNumber = $portalNumber;
    }

    public function getInterval(): ?int
    {
        return $this->interval;
    }

    public function setInterval(?int $interval): void
    {
        $this->interval = $interval;
    }

    public function getTransactionType(): ?int
    {
        return $this->transactionType;
    }

    public function setTransactionType(?int $transactionType): void
    {
        $this->transactionType = $transactionType;
    }

    public function getMarketingType(): ?int
    {
        return $this->marketingType;
    }

    public function setMarketingType(?int $marketingType): void
    {
        $this->marketingType = $marketingType;
    }

    public function getIntervalInDays(): ?int
    {
        return $this->intervalInDays;
    }

    public function setIntervalInDays(?int $intervalInDays): void
    {
        $this->intervalInDays = $intervalInDays;
    }

    public function getDummyValue(): ?string
    {
        return $this->dummyValue;
    }

    public function setDummyValue(?string $dummyValue): void
    {
        $this->dummyValue = $dummyValue;
    }
}
