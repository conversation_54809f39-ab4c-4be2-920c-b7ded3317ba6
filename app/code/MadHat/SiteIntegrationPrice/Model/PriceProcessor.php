<?php
/**
 * Logic apply price to products for Magento 2
 */

namespace MadHat\SiteIntegrationPrice\Model;

use MadHat\SiteIntegrationPrice\Api\Data\PriceDataInterface;
use MadHat\SiteIntegrationProducts\Helper\Data;
use Magento\Catalog\Api\Data\SpecialPriceInterfaceFactory;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Api\SpecialPriceInterface;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Store\Api\WebsiteRepositoryInterface;
use Psr\Log\LoggerInterface;
use MadHat\InventoryImport\Model\Source\ProductStatus;
class PriceProcessor
{
    /**
     * @var SpecialPriceInterfaceFactory
     */
    private SpecialPriceInterfaceFactory $specialPriceFactory;

    /**
     * @var ProductRepositoryInterface
     */
    private ProductRepositoryInterface $productRepository;

    /**
     * @var SpecialPriceInterface
     */
    private SpecialPriceInterface $specialPrice;

    /**
     * @var WebsiteRepositoryInterface
     */
    private WebsiteRepositoryInterface $websiteRepository;

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @var Data
     */
    private Data $siteProductHelper;

    private array $websites = [
        'TechnologyOutlet' => 'base',
        '3DPrima' => 'prima3d'
    ];


    /**
     * @param SpecialPriceInterfaceFactory $specialPriceFactory
     * @param ProductRepositoryInterface $productRepository
     * @param SpecialPriceInterface $specialPrice
     * @param WebsiteRepositoryInterface $websiteRepository
     * @param LoggerInterface $logger
     * @param Data $siteProductHelper
     */
    public function __construct(
        SpecialPriceInterfaceFactory $specialPriceFactory,
        ProductRepositoryInterface $productRepository,
        SpecialPriceInterface $specialPrice,
        WebsiteRepositoryInterface $websiteRepository,
        LoggerInterface $logger,
        Data $siteProductHelper
    )
    {
        $this->specialPriceFactory = $specialPriceFactory;
        $this->productRepository = $productRepository;
        $this->specialPrice = $specialPrice;
        $this->websiteRepository = $websiteRepository;
        $this->logger = $logger;
        $this->siteProductHelper = $siteProductHelper;
    }

    /**
     * @param $item PriceDataInterface
     * @return bool
     *
     * Example:
     * $item = [
     * "ProductNo" => "0634654249111",
     * "ExternalProductNo" => "",
     * "PriceNet" => 39.0,
     * "DiscountPercentage" => 0.0,
     * "TaxClass" => 3011,
     * "CurrencyCode" => "GBP",
     * "CountryCode" => "",
     * "NumberOfUnits" => 0.0,
     * "PortalNumber" => 302,
     * "Interval" => 0,
     * "TransactionType" => 0,
     * "MarketingType" => 0,
     * ]
     */
    public function processItem(PriceDataInterface $item): bool
    {
        try {
            $this->logger->info(__('%1 => %2 All Fine Processing item SKU: %3, DATA: %4',
                __CLASS__,
                __FUNCTION__,
                $item->getProductNo(),
                print_r($item, true)
            ));

            // Set Product price logic HERE
            $product = $this->productRepository->get($item->getProductNo());
            $product->setPrice($item->getPriceNet());
            $product->setSpecialPrice(null);

            $productInvStatus = $product->getData('madhat_inventory_status');

            // $productInvStatus not Expires, Discontinued, Not Released
            if (($item->getPriceNet() > 0) && $productInvStatus) {
                if (($productInvStatus == ProductStatus::VALUE_EXPIRES ||
                    $productInvStatus == ProductStatus::VALUE_DISCONTINUED ||
                    $productInvStatus == ProductStatus::VALUE_NOT_RELEASED))
                {
                    $product->setStatus(Status::STATUS_DISABLED);
                } else {
                    $product->setStatus(Status::STATUS_ENABLED);
                }
            }

            // Set product tax class
            $taxClassId = 0;
            $productTaxMapping = $this->siteProductHelper->getProductTaxCodeMapping();
            if (!empty($productTaxMapping)) {
                $taxClassId = $productTaxMapping[$item->getTaxClass()];
            }
            $product->setTaxClassId($taxClassId);

            $this->productRepository->save($product);

            if ($item->getDiscountPercentage()) {
                $specialPrice = $item->getPriceNet() - ($item->getPriceNet() * ($item->getDiscountPercentage()/100));
                $website = $this->websiteRepository->get($this->websites['TechnologyOutlet']);
                $storeIds = $website->getStoreIds();

                $prices = [];
                foreach ($storeIds as $storeId) {
                    $price = $this->specialPriceFactory->create();
                    $price->setSku($item->getProductNo())
                        ->setPrice($specialPrice)
                        ->setStoreId($storeId);
                    $prices[] = $price;
                }

                $this->specialPrice->update($prices);
            }
        } catch (\Exception $exception) {
            $this->logger->error(__('%1 => %2 Error on process item SKU: %3, ERROR: %4',
                __CLASS__,
                __FUNCTION__,
                $item->getProductNo(),
                $exception->getMessage()
            ));
            return false;
        }
        return true;
    }
}
