<?php
/**
 * Get Price data from RabbitMQ
 */

namespace MadHat\SiteIntegrationPrice\Model\RabbitMQ;

use MadHat\SiteIntegrationPrice\Model\PriceProcessor;
use Magento\Framework\Serialize\Serializer\Json as SerializerJson;
use Psr\Log\LoggerInterface;
use MadHat\DbLogger\Logger\DbLoggerSaver;
use Magento\Store\Model\App\Emulation;
use Magento\Store\Model\StoreManagerInterface;
use MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider;

class PriceConsumer
{
    /**
     * @var LoggerInterface
     */
    private $logger;
    private SerializerJson $jsonSerializer;
    private PriceProcessor $priceProcessor;

    /**
     * @var DbLoggerSaver
     */
    private DbLoggerSaver $dbLoggerSaver;

    /**
     * @var Emulation
     */
    private Emulation $emulation;

    /**
     * @var StoreManagerInterface
     */
    private StoreManagerInterface $storeManager;

    /**
     * PriceConsumer constructor.
     *
     * @param PriceProcessor $priceProcessor
     * @param SerializerJson $jsonSerializer
     * @param LoggerInterface $logger
     * @param DbLoggerSaver $dbLoggerSaver
     * @param Emulation $emulation
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        PriceProcessor $priceProcessor,
        SerializerJson $jsonSerializer,
        LoggerInterface $logger,
        DbLoggerSaver $dbLoggerSaver,
        Emulation $emulation,
        StoreManagerInterface $storeManager
    ) {
        $this->priceProcessor = $priceProcessor;
        $this->jsonSerializer = $jsonSerializer;
        $this->logger = $logger;
        $this->dbLoggerSaver = $dbLoggerSaver;
        $this->emulation = $emulation;
        $this->storeManager = $storeManager;
    }

    /**
     * Consumer process start
     * @param \MadHat\SiteIntegrationPrice\Api\Data\PriceDataInterface[] $message
     * @return void
     */
    public function processMessage(array $message): void
    {
        $this->logger->info(__('%1 => %2 Received Message: %3',
            __CLASS__,
            __FUNCTION__,
            print_r($message, true)
        ));
        try {
            $totalMessages = count($message);
            $successfulMessages = 0;
            $failedMessages = 0;
            $errors = [];

            foreach ($message as $item) {
                try {
                    $this->priceProcessor->processItem($item);
                    $successfulMessages++;
                } catch (\Exception $e) {
                    $failedMessages++;
                    $errors[] = [
                        'Item' => $item,
                        'ErrorMessage' => $e->getMessage()
                    ];
                    $this->logger->error('Error processing item: ' . $e->getMessage());
                }
            }

            // Now, call a function to log the results, similar to 'addDbLoggerStockRecord'
            $this->addDbLoggerPriceRecord($totalMessages, $successfulMessages, $failedMessages, $errors);

        } catch (\InvalidArgumentException $exception) {
            // Handle the exception, such as logging it
            $this->logger->error(__('%1 => %2 ERROR: %3',
                __CLASS__,
                __FUNCTION__,
                $exception->getMessage()
            ));
        }
    }

    /**
     * Function to log the processing results
     *
     * @param int $totalMessages
     * @param int $successfulMessages
     * @param int $failedMessages
     * @param array $errors
     * @return void
     */
    private function addDbLoggerPriceRecord(int $totalMessages, int $successfulMessages, int $failedMessages, array $errors): void
    {
        $message = "Total Messages: " . $totalMessages;
        $message .= " | Successful Messages: " . $successfulMessages;
        $message .= " | Failed Messages: " . $failedMessages;

        if (!empty($errors)) {
            $message .= " | Errors: " . json_encode($errors);
        }

        $defaultStoreId = $this->storeManager->getDefaultStoreView()->getId();

        if (!$defaultStoreId) {
            $defaultStoreId = 1;
        }

        $this->emulation->startEnvironmentEmulation($defaultStoreId);
        $this->dbLoggerSaver->addRecord(
            'Price Import Report',
            $message,
            'NOTICE',
            LogIdentifierProvider::PRICE // Make sure this identifier exists in your LogIdentifierProvider
        );
        $this->emulation->stopEnvironmentEmulation();
    }
}
