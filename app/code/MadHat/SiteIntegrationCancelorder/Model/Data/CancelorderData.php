<?php

namespace MadHat\SiteIntegrationCancelorder\Model\Data;

use MadHat\SiteIntegrationCancelorder\Api\Data\CancelorderDataInterface;
use Magento\Framework\DataObject;

class CancelorderData extends DataObject implements CancelorderDataInterface
{
    public function getVismaOrderNo()
    {
        return $this->getData('VismaOrderNo');
    }

    public function setVismaOrderNo(?int $vismaOrderNo)
    {
        $this->setData('VismaOrderNo', $vismaOrderNo);
    }

    public function getWebOrderNo()
    {
        return $this->getData('WebOrderNo');
    }

    public function setWebOrderNo(?string $webOrderNo)
    {
        $this->setData('WebOrderNo', $webOrderNo);
    }

    public function getExternalOrderNo()
    {
        return $this->getData('ExternalOrderNo');
    }

    public function setExternalOrderNo(?string $externalOrderNo)
    {
        $this->setData('ExternalOrderNo', $externalOrderNo);
    }

    public function getPortal()
    {
        return $this->getData('Portal');
    }

    public function setPortal(?int $portal)
    {
        $this->setData('Portal', $portal);
    }

    public function getStatus()
    {
        return $this->getData('Status');
    }

    public function setStatus(?int $status)
    {
        $this->setData('Status', $status);
    }

    public function getStatusText()
    {
        return $this->getData('StatusText');
    }

    public function setStatusText(?string $statusText)
    {
        $this->setData('StatusText', $statusText);
    }

    public function getOrderType()
    {
        return $this->getData('OrderType');
    }

    public function setOrderType(?int $orderType)
    {
        $this->setData('OrderType', $orderType);
    }

    public function getOrderTypeText()
    {
        return $this->getData('OrderTypeText');
    }

    public function setOrderTypeText(?string $orderTypeText)
    {
        $this->setData('OrderTypeText', $orderTypeText);
    }

    public function getAmountGross()
    {
        return $this->getData('AmountGross');
    }

    public function setAmountGross(?float $amountGross)
    {
        $this->setData('AmountGross', $amountGross);
    }

    public function getAmountNet()
    {
        return $this->getData('AmountNet');
    }

    public function setAmountNet(?float $amountNet)
    {
        $this->setData('AmountNet', $amountNet);
    }

    public function getCostPriceSumNet()
    {
        return $this->getData('CostPriceSumNet');
    }

    public function setCostPriceSumNet(?float $costPriceSumNet)
    {
        $this->setData('CostPriceSumNet', $costPriceSumNet);
    }

    public function getSeller()
    {
        return $this->getData('Seller');
    }

    public function setSeller(?int $seller)
    {
        $this->setData('Seller', $seller);
    }

    public function getOrderDate()
    {
        return $this->getData('OrderDate');
    }

    public function setOrderDate(?string $orderDate)
    {
        $this->setData('OrderDate', $orderDate);
    }

    public function getChangeDate()
    {
        return $this->getData('ChangeDate');
    }

    public function setChangeDate(?int $changeDate)
    {
        $this->setData('ChangeDate', $changeDate);
    }

    public function getPaymentMethod()
    {
        return $this->getData('PaymentMethod');
    }

    public function setPaymentMethod(\MadHat\SiteIntegrationCancelorder\Api\Data\PaymentMethodInterface $paymentMethod = null)
    {
        $this->setData('PaymentMethod', $paymentMethod);
    }

    public function getCurrencyISO()
    {
        return $this->getData('CurrencyISO');
    }

    public function setCurrencyISO(?string $currencyISO)
    {
        $this->setData('CurrencyISO', $currencyISO);
    }

    public function getCountryISO()
    {
        return $this->getData('CountryISO');
    }

    public function setCountryISO(?string $countryISO)
    {
        $this->setData('CountryISO', $countryISO);
    }

    public function getShippingCountryISO()
    {
        return $this->getData('ShippingCountryISO');
    }

    public function setShippingCountryISO(?string $shippingCountryISO)
    {
        $this->setData('ShippingCountryISO', $shippingCountryISO);
    }

    public function getShipmentStatus()
    {
        return $this->getData('ShipmentStatus');
    }

    public function setShipmentStatus(?int $shipmentStatus)
    {
        $this->setData('ShipmentStatus', $shipmentStatus);
    }

    public function getFulfilmentWorkflow()
    {
        return $this->getData('FulfilmentWorkflow');
    }

    public function setFulfilmentWorkflow(?int $fulfilmentWorkflow)
    {
        $this->setData('FulfilmentWorkflow', $fulfilmentWorkflow);
    }

    public function getOrderRowsData()
    {
        return $this->getData('OrderRowsData');
    }

    public function setOrderRowsData(?array $orderRowsData)
    {
        $this->setData('OrderRowsData', $orderRowsData);
    }

    public function getConsignments()
    {
        return $this->getData('Consignments');
    }

    public function setConsignments(?array $consignments)
    {
        $this->setData('Consignments', $consignments);
    }

    public function getProductShipments()
    {
        return $this->getData('ProductShipments');
    }

    public function setProductShipments(?array $productShipments)
    {
        $this->setData('ProductShipments', $productShipments);
    }

    public function getBillings()
    {
        return $this->getData('Billings');
    }

    public function setBillings(?array $billings)
    {
        $this->setData('Billings', $billings);
    }
}
