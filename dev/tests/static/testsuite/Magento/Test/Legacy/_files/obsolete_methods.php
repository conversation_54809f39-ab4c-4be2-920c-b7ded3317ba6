<?php
/**
 * Obsolete methods
 * Format: array(<method_name = ''>[, <class_scope> = ''[, <replacement>[, <is_deprecated>]]])
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

return [
    ['__get', 'Magento\Framework\DataObject'],
    ['__set', 'Magento\Framework\DataObject'],
    ['_addItem', 'Magento\Theme\Block\Html\Head'],
    ['_addLink', 'Magento\Customer\Block\Account\Link'],
    ['_addMinimalPrice', 'Magento\Catalog\Model\ResourceModel\Product\Collection'],
    ['_addTaxPercents', 'Magento\Catalog\Model\ResourceModel\Product\Collection'],
    ['_afterSaveCommit', 'Magento\Framework\Model\AbstractModel'],
    ['_afterSetConfig', 'Magento\Eav\Model\Entity\AbstractEntity'],
    ['getAttributeCodes', 'Magento\Eav\Model\Entity\Type'],
    ['_loadTypeAttributes', 'Magento\Eav\Model\ResourceModel\Entity\Attribute'],
    ['preloadAttributes', 'Magento\Eav\Model\Entity\Type\Config'],
    ['_aggregateByOrderCreatedAt', 'Magento\SalesRule\Model\ResourceModel\Report\Rule'],
    ['_amountByCookies', 'Magento\Sendfriend\Model\Sendfriend'],
    ['setCookie', 'Magento\Sendfriend\Model\Sendfriend'],
    ['getCookie', 'Magento\Sendfriend\Model\Sendfriend'],
    ['setRemoteAddr', 'Magento\Sendfriend\Model\Sendfriend'],
    ['getRemoteAddr', 'Magento\Sendfriend\Model\Sendfriend'],
    ['setWebsiteId', 'Magento\Sendfriend\Model\Sendfriend'],
    ['getWebsiteId', 'Magento\Sendfriend\Model\Sendfriend'],
    ['_applyClassRewrites', 'Magento\Core\Model\Config'],
    ['_applyCustomDesignSettings'],
    ['_applyCustomizationFiles', 'Magento\Core\Model\Theme'],
    ['_applyDesign', 'Magento\Catalog\Model\Design'],
    ['_applyDesignRecursively', 'Magento\Catalog\Model\Design'],
    ['_avoidDoubleTransactionProcessing'],
    ['_beforeChildToHtml'],
    ['_beforeMove', 'Magento\Catalog\Model\ResourceModel\Category\Tree'],
    ['_getIsActiveAttributeId', 'Magento\Catalog\Model\ResourceModel\Category\Tree', 'Magento\Catalog\Model\ResourceModel\Category::getIsActiveAttributeId'],
    ['_getIsActiveAttributeId', 'Magento\Catalog\Model\ResourceModel\Category', 'Magento\Catalog\Model\ResourceModel\Category::getIsActiveAttributeId'],
    ['_bytesToMbytes', 'Magento\Catalog\Model\Product\Option\Type\File'],
    ['_calculatePrice', 'Magento\Quote\Model\Quote\Item\AbstractItem'],
    ['_canBeStoreCodeInUrl', 'Magento\Framework\App\Request\Http'],
    ['_canShowField', 'Magento\Backend\Block\System\Config\Form'],
    ['_canUseCacheForInit', 'Magento\Core\Model\Config'],
    ['_canUseLocalModules'],
    ['_checkCookieStore', 'Magento\Core\Model\App'],
    [
        '_checkCookieStore',
        'Magento\Core\Model\Store\Storage\Db',
        'Magento\Store\Model\StorageFactory::_checkCookieStore',
    ],
    ['_checkGetStore', 'Magento\Core\Model\App'],
    [
        '_checkGetStore',
        'Magento\Core\Model\Store\Storage\Db',
        'Magento\Store\Model\StorageFactory::_checkRequestStore',
    ],
    ['_checkUrlSettings', 'Magento\Backend\Controller\Adminhtml\Action'],
    ['_collectOrigData', 'Magento\Catalog\Model\ResourceModel\AbstractResource'],
    ['_decodeFilter', 'Magento\Backend\Block\Widget\Grid'],
    ['_decodeInput', 'Magento\Catalog\Controller\Adminhtml\Product'],
    ['_emailOrderConfirmation', 'Magento\Checkout\Model\Type\AbstractType'],
    ['_escapeValue', 'Magento\Backend\Block\Widget\Grid\Column\Filter\AbstractFilter'],
    ['_extractData', 'Magento\Framework\ObjectManager\Config\Reader\Dom'],
    ['_filterPostData', 'Magento\Catalog\Controller\Adminhtml\Product\Attribute'],
    ['_generateCssHtml', 'Magento\Theme\Block\Html\Head'],
    ['_generateJsHtml', 'Magento\Theme\Block\Html\Head'],
    ['_getAddressTaxRequest', 'Magento\Tax\Model\Sales\Total\Quote\Shipping'],
    ['_getAggregationPerStoreView'],
    ['_getAttributeFilterBlockName'],
    ['_getAttributeFilterBlockName', 'Magento\LayeredNavigation\Block\Navigation'],
    ['_getAttributeFilterBlockName', 'Magento\CatalogSearch\Block\Layer'],
    ['_getAvailable', 'Magento\GiftMessage\Model\Observer'],
    ['_getBytesIniValue', 'Magento\Catalog\Model\Product\Option\Type\File'],
    ['_getCacheKey', 'Magento\Catalog\Model\Layer\Filter\Price'],
    ['_getCacheLockId', 'Magento\Core\Model\Config'],
    ['_getCarrier', 'Magento\Shipping\Model\Config', 'Magento\Shipping\Model\CarrierFactory::create||get'],
    ['_getChildHtml'],
    ['_getConfig', 'Magento\Theme\Helper\Layout'],
    ['_getCollapseState', 'Magento\Backend\Block\System\Config\Form\Fieldset', '_isCollapseState'],
    ['_getCollectionNames', 'Magento\Backend\Controller\Report\Sales'],
    ['_getConnectionAdapterClassName', 'Magento\Framework\App\ResourceConnection'],
    ['_getConnenctionType', 'Magento\Install\Model\Installer\Db'],
    ['_getDateFromToHtml', 'Magento\ImportExport\Block\Adminhtml\Export\Filter'],
    ['_getDeclaredModuleFiles', 'Magento\Core\Model\Config'],
    ['_getDesignChange', 'Magento\Framework\App\Area'],
    ['_getDom', 'Magento\Backend\Model\Menu\Config'],
    ['_getExistingBasePopularity'],
    ['_getFieldTableAlias', 'Magento\Newsletter\Model\ResourceModel\Subscriber\Collection'],
    ['_getForeignKeyName', 'Magento\Framework\DB\Adapter\Pdo\Mysql'],
    ['_getGiftmessageSaveModel', 'Magento\Sales\Block\Adminhtml\Order\Create\Search\Grid'],
    ['_getGlobalAggregation'],
    ['_getGroupByDateFormat', 'Magento\Log\Model\Resource\Visitor\Collection'],
    ['_getIdAttributes', 'Magento\Framework\ObjectManager\Config\Reader\Dom'],
    ['_getInitialXml', 'Magento\Framework\ObjectManager\Config\Reader\Dom'],
    ['_getInputHtml', 'Magento\ImportExport\Block\Adminhtml\Export\Filter'],
    ['_getItemPriceBlock', 'Magento\Wishlist\Block\AbstractBlock'],
    ['getIsActive', 'Magento\Store\Model\Store', 'isActive'],
    ['_getLabelForStore', 'Magento\Catalog\Model\ResourceModel\Eav\Attribute'],
    ['_getMultiSelectHtml', 'Magento\ImportExport\Block\Adminhtml\Export\Filter'],
    ['_getNumberFromToHtml', 'Magento\ImportExport\Block\Adminhtml\Export\Filter'],
    ['_getPathInScope', 'Magento\Core\Model\Config'],
    ['_getPriceFilter', 'Magento\LayeredNavigation\Block\Navigation'],
    ['_getProcessor', 'Magento\Framework\App\Cache'],
    ['getPublicFileUrl', 'Magento\Framework\View\Url', 'Magento\Framework\Url::getBaseUrl'],
    ['_getRangeByType', 'Magento\Log\Model\Resource\Visitor\Collection'],
    ['_getRecentProductsCollection'],
    ['_getRequestModel', 'Magento\Authorizenet\Model\Directpost'],
    ['_getResource', 'Magento\Core\Model\Resource\Setup'],
    ['_getScopeCode', 'Magento\Core\Model\Config'],
    ['_getSectionConfig', 'Magento\Core\Model\Config'],
    ['_getSelectHtml', 'Magento\ImportExport\Block\Adminhtml\Export\Filter'],
    ['_getSetData', 'Magento\Backend\Block\Catalog\Product\Attribute\Set\Main'],
    ['_getSession', 'Magento\Paygate\Model\Authorizenet', 'Magento_Paygate_Model_Authorizenet::_session'],
    ['_getSHAInSet', '', 'Magento_Ogone_Model_Api::getHash'],
    ['_getStoreByGroup', 'Magento\Core\Model\App'],
    [
        '_getStoreByGroup',
        'Magento\Core\Model\Store\Storage\Db',
        'Magento\Store\Model\StorageFactory::_getStoreByGroup',
    ],
    ['_getStoreByWebsite', 'Magento\Core\Model\App'],
    [
        '_getStoreByWebsite',
        'Magento\Core\Model\Store\Storage\Db',
        'Magento\Store\Model\StorageFactory::_getStoreByWebsite',
    ],
    ['_getStoreTaxRequest', 'Magento\Tax\Model\Sales\Total\Quote\Shipping'],
    ['_getUploadMaxFilesize', 'Magento\Catalog\Model\Product\Option\Type\File'],
    ['_hookQueries', 'Magento\Core\Model\Resource\Setup'],
    ['_importAddress', 'Magento\Paypal\Model\Api\Nvp'],
    ['_inheritDesign', 'Magento\Catalog\Model\Design'],
    ['_initLayoutMessages', 'Magento\Framework\App\Action\Action'],
    ['_initModulesPreNamespaces', 'Magento\Core\Model\Config'],
    ['_initOrder', 'Magento\Shipping\Block\Tracking\Popup'],
    ['_initShipment', 'Magento\Shipping\Block\Tracking\Popup'],
    ['_inludeControllerClass', '', '_includeControllerClass'],
    ['_isApplyDesign', 'Magento\Catalog\Model\Design'],
    ['_isApplyFor', 'Magento\Catalog\Model\Design'],
    ['_isPositiveDecimalNumber', 'Magento\OfflineShipping\Model\ResourceModel\Carrier\Tablerate'],
    ['_isRuntimeValidated', 'Magento\Framework\ObjectManager\Config\Reader\Dom'],
    ['_loadCache', 'Magento\Backend\Model\Menu\Config'],
    ['_loadCache', 'Magento\Core\Model\Config'],
    ['_loadDeclaredModules', 'Magento\Core\Model\Config'],
    ['_loadInstallDate', 'Magento\Core\Model\Config'],
    ['_loadLocalConfig', 'Magento\Core\Model\Config'],
    ['_loadOldRates', 'Magento\Tax\Model\ResourceModel\Setup'],
    ['_loadSectionCache', 'Magento\Core\Model\Config'],
    ['_needSubtractShippingTax'],
    ['_needSubtractTax'],
    ['_needToAddDummy'],
    ['_needToAddDummyForShipment'],
    ['_outTemplate', 'Magento\Backend\App\AbstractAction'],
    ['getCustomer', 'Magento\Sales\Block\Adminhtml\Order\Create\AbstractCreate', 'getCustomerId'],
    ['_getAddressForm', 'Magento\Sales\Block\Adminhtml\Order\Create\Form\Address'],
    ['_parseDescription', 'Magento\Sales\Model\Order\Pdf\Items\AbstractItems'],
    ['_parsePackageTheme', 'Magento\Widget\Model\Widget\Instance'],
    ['_parseXmlTrackingResponse', 'Magento\Fedex\Model\Carrier'],
    ['_parseXml', 'Magento\Fedex\Model\Carrier'],
    ['_prepareCondition', 'Magento\CatalogSearch\Model\Advanced'],
    ['_prepareConfigurableProductData', 'Magento\CatalogImportExport\Model\Export\Product'],
    ['_prepareConfigurableProductPrice', 'Magento\CatalogImportExport\Model\Export\Product'],
    ['_prepareOptionsForCart', 'Magento\Catalog\Model\Product\Type\AbstractType'],
    ['_preparePackageTheme', 'Magento\Widget\Model\Widget\Instance'],
    ['_processItem', 'Magento\Weee\Model\Total\Quote\Weee'],
    ['_processShippingAmount'],
    ['_processValidateCustomer', 'Magento\Checkout\Model\Type\Onepage'],
    ['_putCustomerIntoQuote', 'Magento\Sales\Model\AdminOrder\Create'],
    ['_quoteRow', 'Magento\Backup\Model\ResourceModel\Db'],
    ['_recollectItem', 'Magento\Tax\Model\Sales\Total\Quote\Subtotal'],
    ['_removeCache', 'Magento\Core\Model\Config'],
    ['_removeLink', 'Magento\Customer\Block\Account\Link'],
    ['_resetItemPriceInclTax'],
    ['_saveCache', 'Magento\Backend\Model\Menu\Config'],
    ['_saveCache', 'Magento\Core\Model\Config'],
    ['_saveCustomerAfterOrder', 'Magento\Sales\Model\AdminOrder\Create'],
    ['_getCustomerAddressForm', 'Magento\Sales\Model\AdminOrder\Create'],
    ['_saveCustomers', 'Magento\Sales\Model\AdminOrder\Create'],
    ['_saveSectionCache', 'Magento\Core\Model\Config'],
    ['_sendUploadResponse', 'Magento\Backend\Controller\Customer'],
    ['isInStore', 'Magento\Customer\Model\Customer'],
    ['_sendUploadResponse', 'Magento\Newsletter\Controller\Adminhtml\Subscriber'],
    ['_setAttribteValue'],
    ['_sort', 'Magento\Backend\Model\Config\Structure\Converter'],
    [
        'submitOrder',
        'Magento\Quote\Model\Service\Quote',
        'Magento\Quote\Model\Service\Quote::submitOrderWithDataObject',
    ],
    [
        'submitAll',
        'Magento\Quote\Model\Service\Quote',
        'Magento\Quote\Model\Service\Quote::submitAllWithDataObject',
    ],
    [
        'exportCustomerAddress',
        'Magento\Quote\Model\Quote\Address',
        'Magento\Quote\Model\Quote\Address::exportCustomerAddressData',
    ],
    ['_toHtml', 'Magento\Backend\Block\Widget\Container'],
    ['_unhookQueries', 'Magento\Core\Model\Resource\Setup'],
    ['_updateMediaPathUseRewrites', 'Magento\Store\Model\Store', '_getMediaScriptUrl'],
    ['_usePriceIncludeTax'],
    ['addAccountLink', 'Magento\Customer\Block\Account\Link'],
    ['addAllowedModules', 'Magento\Core\Model\Config'],
    ['addAuthLink', 'Magento\Customer\Block\Account\Link'],
    ['addBackupedFilter'],
    ['addCartLink', 'Magento\Checkout\Block\Links'],
    ['addCheckoutLink', 'Magento\Checkout\Block\Links'],
    ['addColumnInputName', 'Magento\Backend\Block\Widget\Grid\Serializer'],
    ['addColumnRender', 'Magento\Sales\Block\Adminhtml\Items\AbstractItems', 'setColumnRenders'],
    ['addConfigField', 'Magento\Core\Model\Resource\Setup'],
    ['addConstraint', 'Magento\Framework\DB\Adapter\Pdo\Mysql'],
    ['addCss', 'Magento\Theme\Block\Html\Head'],
    ['addCssIe', 'Magento\Theme\Block\Html\Head'],
    ['addCustomerData', 'Magento\Catalog\Model\Product\Item', 'setCustomerId'],
    ['addCustomersToAlertQueueAction'],
    ['addCustomerToSegments'],
    ['addHandle', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['addItemPriceBlockType'],
    ['addItemRender', 'Magento\Sales\Block\Adminhtml\Items\AbstractItems'],
    ['addItemRender', 'Magento\Checkout\Block\Cart\AbstractCart'],
    ['addItemRender', 'Magento\Sales\Block\Items\AbstractItems'],
    ['addJs', 'Magento\Theme\Block\Html\Head'],
    ['addJsIe', 'Magento\Theme\Block\Html\Head'],
    ['addKey', 'Magento\Framework\DB\Adapter\Pdo\Mysql'],
    ['addLinkRel', 'Magento\Theme\Block\Html\Head'],
    ['addLogInLink', 'Magento\Customer\Block\Account\Link'],
    ['addModule', 'Magento\Core\App\Router\Base'],
    ['addRouter', 'Magento\Framework\App\FrontController'],
    ['addOptionRenderer', 'Magento\Catalog\Block\Product\View\Options'],
    ['addPageHandles', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['addPagerLimit', 'Magento\Catalog\Block\Product\ProductList\Toolbar'],
    ['addRegisterLink', 'Magento\Customer\Block\Account\Link'],
    ['addRenderer', 'Magento\Bundle\Block\Catalog\Product\View\Type\Bundle'],
    ['addReviewSummaryTemplate', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['addRowItemRender', 'Magento\Multishipping\Block\Checkout\Overview'],
    ['addSaleableFilterToCollection'],
    ['addSearchQfFilter'],
    ['addTemplateData', 'Magento\Newsletter\Model\Queue'],
    ['addToAlersAction'],
    ['addToChildGroup'],
    ['addToParentGroup', '\Magento\Core\Block\AbstractBlock'],
    ['addUpdate', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['addVisibleFilterToCollection', 'Magento\Catalog\Model\Product\Status'],
    [
        'addVisibleInCatalogFilterToCollection',
        '',
        '$collection->setVisibility(\Magento\Catalog\Model\Product\Visibility->getVisibleInCatalogIds());',
    ],
    [
        'addVisibleInSearchFilterToCollection',
        '',
        '$collection->setVisibility(\Magento\Catalog\Model\Product\Visibility->getVisibleInSearchIds());',
    ],
    [
        'addVisibleInSiteFilterToCollection',
        '',
        '$collection->setVisibility(\Magento\Catalog\Model\Product\Visibility->getVisibleInSiteIds());',
    ],
    ['addWishListSortOrder', 'Magento\Wishlist\Model\ResourceModel\Item\Collection'],
    [
        'aggregateSalesReportShipmentData',
        '\Magento\Sales\Model\Observer',
        '\Magento\Shipping\Model\Observer::aggregateSalesReportShipmentData',
    ],
    ['applyAllDataUpdates', 'Magento\Core\Model\Resource\Setup'],
    ['applyAllUpdates', 'Magento\Core\Model\Resource\Setup'],
    ['applyDesign', 'Magento\Catalog\Model\Design'],
    ['asArray', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['asSimplexml', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['asString', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['authAdmin'],
    ['authFailed', '', '\Magento\Framework\HTTP\Authentication::setAuthenticationFailed'],
    ['authFrontend'],
    ['authValidate', '', '\Magento\Framework\HTTP\Authentication::getCredentials'],
    ['bundlesAction', 'Magento\Catalog\Controller\Adminhtml\Product'],
    ['calcTaxAmount', 'Magento\Quote\Model\Quote\Item\AbstractItem'],
    ['callbackQueryHook', 'Magento\Core\Model\Resource\Setup'],
    ['canCreateUser', 'Magento\User\Model\ResourceModel\User'],
    ['canPrint', 'Magento\Checkout\Block\Onepage\Success'],
    [
        'getAllServiceDataInterfaces',
        'Magento\Framework\Webapi\CustomAttributeTypeLocatorInterface',
        'Magento\Framework\Webapi\CustomAttribute\ServiceTypeListInterface::getDataTypes()'
    ],
    [
        'canTestHeaders',
        'Magento\TestFramework\Bootstrap',
        'Magento_TestFramework_Helper_Bootstrap::canTestHeaders',
    ],
    ['catalogCategoryChangeProducts', 'Magento\Catalog\Model\Product\Flat\Observer'],
    ['catalog' . 'EventProductCollectionAfterLoad', 'Magento\GiftMessage\Model\Observer'],
    ['catalogProductCompareClean', 'Magento\Catalog\Model\Observer'],
    ['changeLocaleAction', 'Magento\Backend\Controller\Adminhtml\Index'],
    ['chechAllowedExtension'],
    ['checkConfigurableProducts', 'Magento\Eav\Model\ResourceModel\Entity\Attribute\Collection'],
    ['checkDatabase', 'Magento\Install\Model\Installer\Db'],
    ['checkDateTime', 'Magento\Framework\Stdlib\DateTime\DateTime'],
    ['chooseTemplate', 'Magento\Checkout\Block\Cart'],
    ['cleanCache', 'Magento\Core\Model\Config'],
    ['cleanCache', '\Magento\Core\Model\Observer', '\Magento\Backend\Model\Observer'],
    ['cleanDbRow', 'Magento\Framework\App\ResourceConnection'],
    ['cleanMergedJsCss', 'Magento\Core\Model\Design\Package', 'Magento\Framework\View\Asset\MergeService'],
    ['cleanMergedJsCss', 'Magento\Core\Model\Design\Package\Proxy', 'Magento\Framework\View\Asset\MergeService'],
    [
        'cleanMergedJsCss',
        'Magento\Core\Model\Design\PackageInterface',
        'Magento\Framework\View\Asset\MergeService',
    ],
    ['cleanVarFolder', '', 'Magento\Framework\Filesystem\Io\File::rmdirRecursive()'],
    [
        'cleanVarSubFolders',
        '',
        '\Magento\Framework\Filesystem::getDirectoryRead(\Magento\Framework\Filesystem::VAR_DIR)::search())',
    ],
    ['collectRoutes', 'Magento\Backend\App\Router'],
    ['collectRoutes', 'Magento\Core\App\Router\Base'],
    ['composeLocaleHierarchy', 'Magento\Translation\Helper\Data'],
    ['convertOldTaxData', 'Magento\Tax\Model\ResourceModel\Setup'],
    ['copyFieldset', 'Magento\Core\Helper\Data', 'copyFieldsetToTarget'],
    ['copyQuoteFilesToOrderFiles', 'Magento\Catalog\Model\Product\Option\Observer'],
    ['countChildren', 'Magento\Core\Block\AbstractBlock'],
    ['crear'],
    ['createDirIfNotExists', '', 'mkdir()'],
    ['currency', 'Magento\Core\Helper\Data', 'Magento\Framework\Pricing\Helper\Data::currency'],
    ['currencyByStore', 'Magento\Core\Helper\Data', 'Magento\Framework\Pricing\Helper\Data::currencyByStore'],
    ['debugRequest', 'Magento\Paypal\Model\Api\Standard'],
    ['decodeFilter', 'Magento\Backend\Helper\Data'],
    ['decorateArray', 'Magento\Core\Helper\Data', 'Magento\Framework\Stdlib\ArrayUtils::decorateArray'],
    ['deleteAction', 'Magento\Catalog\Controller\Adminhtml\Product'],
    ['deleteConfig', 'Magento\Core\Model\Config'],
    ['deleteProductPrices', 'Magento\Catalog\Model\ResourceModel\Product\Attribute\Backend\Tierprice'],
    ['display', 'Magento\Framework\Image\Adapter\AbstractAdapter', 'getImage()'],
    ['displayFullSummary', 'Magento\Tax\Model\Config'],
    ['displayZeroTax', 'Magento\Tax\Model\Config'],
    ['drawItem', 'Magento\Catalog\Block\Navigation'],
    ['getStoreCategories', 'Magento\Catalog\Block\Navigation'],
    ['_getItemPosition', 'Magento\Catalog\Block\Navigation'],
    ['_renderCategoryMenuItemHtml', 'Magento\Catalog\Block\Navigation'],
    ['getCurrentCategoryPath', 'Magento\Catalog\Block\Navigation'],
    [
        'getIsMessagesAvailable',
        'Magento\GiftMessage\Helper\Message',
        'Magento\GiftMessage\Helper\Message::isMessagesAllowed',
    ],
    ['drawOpenCategoryItem', 'Magento\Catalog\Block\Navigation'],
    ['renderCategoriesMenuHtml', 'Magento\Catalog\Block\Navigation'],
    ['dropKey', 'Magento\Framework\DB\Adapter\Pdo\Mysql'],
    ['escapeJs', 'Magento\Backend\Block\Catalog\Product\Edit\Tab\Super\Config'],
    ['eventClean', 'Magento\Reports\Model\Event\Observer'],
    ['exportOrderedCsvAction'],
    ['exportOrderedExcelAction'],
    ['fetchItemsCount', 'Magento\Wishlist\Model\ResourceModel\Wishlist'],
    ['fetchRuleRatesForCustomerTaxClass'],
    ['fetchUpdatesByHandle', 'Magento\Core\Model\Resource\Layout', 'Magento\Widget\Model\ResourceModel\Layout\Update'],
    ['flush', 'Magento\Framework\App\Cache', 'Magento_Cache_FrontendInterface::clean()'],
    ['flush', 'Magento\Framework\App\Cache\Proxy', 'Magento_Cache_FrontendInterface::clean()'],
    ['flush', 'Magento\Framework\App\CacheInterface', 'Magento_Cache_FrontendInterface::clean()'],
    [
        'getProductTypes',
        'Magento\ImportExport\Model\Import\Config',
        'Magento\ImportExport\Model\Import\Config::getEntityTypes()',
    ],
    [
        'getProductTypes',
        '\Magento\ImportExport\Model\Import\ConfigInterface',
        '\Magento\ImportExport\Model\Import\ConfigInterface::getEntityTypes()',
    ],
    [
        'getProductTypes',
        'Magento\ImportExport\Model\Export\Config',
        'Magento\ImportExport\Model\Export\Config::getEntityTypes()',
    ],
    [
        'getProductTypes',
        '\Magento\ImportExport\Model\Export\ConfigInterface',
        '\Magento\ImportExport\Model\Export\ConfigInterface::getEntityTypes()',
    ],
    ['forsedSave'],
    [
        'formatCurrency',
        'Magento\Core\Helper\Data',
        '\Magento\Framework\Pricing\PriceCurrencyInterface::convertAndFormat',
    ],
    ['formatPrice', 'Magento\Core\Helper\Data', '\Magento\Framework\Pricing\PriceCurrencyInterface::format'],
    ['generateBlocks', '', 'generateElements()'],
    ['getAbsolutePath', '', 'normalizePath'],
    ['getAccount', 'Magento\GoogleAnalytics\Block\Ga'],
    ['getAclAssert', 'Magento\Admin\Model\Config'],
    ['getAclPrivilegeSet', 'Magento\Admin\Model\Config'],
    ['getAclResourceList', 'Magento\Admin\Model\Config'],
    ['getAclResourceTree', 'Magento\Admin\Model\Config'],
    ['getAddNewButtonHtml', 'Magento\Backend\Block\Catalog\Product'],
    ['getAddNewButtonHtml', 'Magento\Eav\Block\Adminhtml\Attribute\Edit\Options\AbstractOptions'],
    ['getAddToCartItemUrl', 'Magento\Wishlist\Block\Customer\Sidebar'],
    ['getAddToCartUrlBase64', '', '_getAddToCartUrl'],
    ['getAllEntityIds', 'Magento\Rss\Model\Resource\Order'],
    ['getAllEntityTypeCommentIds', 'Magento\Rss\Model\Resource\Order'],
    ['getAllOrderEntityIds', 'Magento\Rss\Model\Resource\Order'],
    ['getAllOrderEntityTypeIds', 'Magento\Rss\Model\Resource\Order'],
    ['getAnonSuffix'],
    ['getAttributesById', 'Magento\Eav\Model\Entity\AbstractEntity'],
    ['getAttributeDataModelFactory', 'Magento\Eav\Model\Validator\Attribute\Data'],
    ['getAttributes', 'Magento\Customer\Helper\Address'],
    ['getAttributesJson', 'Magento\Backend\Block\Catalog\Product\Edit\Tab\Super\Config', 'getAttributes'],
    ['getBaseTaxAmount', 'Magento\Quote\Model\Quote\Item\AbstractItem'],
    ['getBlockClassName', 'Magento\Core\Model\Config'],
    ['getButtonsHtml', 'Magento_Bundle_Block_Adminhtml_Catalog_Product_Edit_Tab_Bundle_Option_Search'],
    ['getCache', 'Magento\Core\Model\Config'],
    ['getCacheTypes', 'Magento\Core\Helper\Data', '\Magento\Framework\App\Cache\TypeListInterface::getTypeLabels'],
    ['getCacheBetaTypes'],
    ['getChangeLocaleUrl', 'Magento\Backend\Block\Page\Footer'],
    ['getCheckoutMehod', 'Magento\Checkout\Model\Type\Onepage'],
    ['getChildGroup', '', 'Magento_Core_Block_AbstractBlock::getGroupChildNames()'],
    ['getConfig', 'Magento\Cms\Model\Wysiwyg\Images\Storage'],
    ['getConfigAsArray', 'Magento\Cms\Model\Wysiwyg\Images\Storage'],
    ['getConfig', 'Magento\Eav\Model\Entity\Attribute\AbstractAttribute'],
    ['getConfigAsXml', 'Magento\Widget\Model\Widget'],
    ['getConfigData', 'Magento\Cms\Model\Wysiwyg\Images\Storage'],
    ['getConfigDataModel', 'Magento\Core\Model\Config'],
    ['getConnectionTypeInstance', 'Magento\Framework\App\ResourceConnection'],
    ['getContainers', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['getCurrentUrl', 'Magento\Core\Helper\Url', 'Magento\Framework\Url'],
    ['getHomeUrl', 'Magento\Core\Helper\Url'],
    ['getCustomerData', 'Magento\Sales\Block\Adminhtml\Order\Create\Form\Account'],
    ['getDataForSave', 'Magento\Wishlist\Model\Item'],
    ['getDataMaxSize'],
    ['getDataMaxSizeInBytes', 'Magento\Backend\Block\Media\Uploader', 'Magento_File_Size::getMaxFileSize()'],
    ['getDbAdapter', 'Magento\Framework\App\Cache'],
    ['getDbAdapter', 'Magento\Framework\App\Cache\Proxy'],
    ['getDbAdapter', 'Magento\Framework\App\CacheInterface'],
    [
        'getDbVendorName',
        'Magento\TestFramework\Bootstrap',
        'Magento_TestFramework_Helper_Bootstrap::getDbVendorName',
    ],
    ['getDebug', 'Magento\Ogone\Model\Api'],
    ['getDebug', 'Magento\Paypal\Model\Api\AbstractApi'],
    ['getDefaultBasePath', 'Magento\Store\Model\Store'],
    ['getDefaultCountry', 'Magento\Core\Helper\Data', 'Magento\Directory\Helper\Data::getDefaultCountry'],
    ['getDeleteUrl', 'Magento\Backend\Block\Catalog\Product\Edit'],
    ['getDirectOutput', 'Magento\Framework\View\Element\Template'],
    ['getDirectOutput', 'Magento\Framework\View\Layout'],
    ['getDirectOutput', 'Magento\Framework\View\LayoutInterface'],
    ['getDistroServerVars', 'Magento\Core\Model\Config', 'getDistroBaseUrl'],
    ['getDuplicateButtonHtml', 'Magento\Catalog\Block\Adminhtml\Product\Edit'],
    ['getElementBackendConfig', 'Magento\Paypal\Helper\Data'],
    ['getElementClass', 'Magento\Core\Model\Layout\Update'],
    ['getEngineFactory', 'Magento\Framework\View\Element\Template\Context', 'getEnginePool'],
    ['getEntityIdsToIncrementIds', 'Magento\Rss\Model\Resource\Order'],
    ['getEntityTypeIdsToTypes', 'Magento\Rss\Model\Resource\Order'],
    ['getEventConfig', 'Magento\Core\Model\Config'],
    ['getExtensionsForCheck'],
    ['getFacets'],
    ['getFallbackTheme'],
    ['getFileLayoutUpdatesXml', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['getFormated', '', "getFormated(true) -> format('html'), getFormated() -> format('text')"],
    ['getFormObject', 'Magento\Backend\Block\Widget\Form'],
    ['getGiftmessageHtml', 'Magento\Sales\Block\Adminhtml\Order\View\Tab\Info'],
    ['getHandles', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['getHeaderCssClass', 'Magento_Bundle_Block_Adminhtml_Catalog_Product_Edit_Tab_Bundle_Option_Search'],
    ['getHeaderText', 'Magento_Bundle_Block_Adminhtml_Catalog_Product_Edit_Tab_Bundle_Option_Search'],
    ['getHelperClassName', 'Magento\Core\Model\Config'],
    ['getHtmlFormat', 'Magento\Customer\Model\Address\AbstractAddress'],
    [
        'getInitParams',
        'Magento\TestFramework\Bootstrap',
        'Magento_TestFramework_Helper_Bootstrap::getAppInitParams',
    ],
    ['getInstallDate', 'Magento\Core\Model\Config'],
    [
        'getInstallDir',
        'Magento\TestFramework\Bootstrap',
        'Magento_TestFramework_Helper_Bootstrap::getAppInstallDir',
    ],
    ['getInstance', 'Magento\TestFramework\Bootstrap', 'Magento_TestFramework_Helper_Bootstrap::getInstance'],
    ['getIsActiveAanalytics', '', 'getOnsubmitJs'],
    ['getIsAjaxRequest', 'Magento\Framework\Translate\Inline'],
    ['getIsEngineAvailable'],
    ['getIsGlobal', 'Magento\Eav\Model\Entity\Attribute\AbstractAttribute'],
    ['getIsInStock', 'Magento\Checkout\Block\Cart\Item\Renderer'],
    ['getIsGrouped', 'Magento\Catalog\Block\Adminhtml\Product\Edit'],
    ['getItemRender', 'Magento\Checkout\Block\Cart\AbstractCart'],
    ['getItemRendererInfo', 'Magento\Checkout\Block\Cart\AbstractCart'],
    ['getKeyList', 'Magento\Framework\DB\Adapter\Pdo\Mysql'],
    ['getLanguageSelect', 'Magento\Backend\Block\Page\Footer'],
    ['getLayoutFilename', '', 'getFilename'],
    ['getLifeTime', 'Magento\Core\Model\Resource\Session'],
    ['getLocaleBaseDir', 'Magento\Core\Model\Design\Package'],
    ['getMail', 'Magento\Newsletter\Model\Template'],
    ['getMaxQueryLenght'],
    ['getMaxUploadSize', 'Magento\ImportExport\Helper\Data', 'getMaxUploadSizeMessage'],
    ['getMenuConfigurationFiles', 'Magento\Backend\Model\Menu\Config'],
    ['getMenuItemLabel', 'Magento\Admin\Model\Config'],
    ['getMergedCssUrl'],
    ['getMergedJsUrl'],
    ['getMessageUserCreationProhibited', 'Magento\User\Model\ResourceModel\User'],
    ['getMinQueryLenght'],
    ['getModelClassName', 'Magento\Core\Model\Config'],
    ['getModuleByName', 'Magento\Core\App\Router\Base'],
    ['getModuleConfigurationFiles', 'Magento\Core\Model\Config'],
    ['getModuleSetup', 'Magento\Core\Model\Config'],
    ['getNeedUsePriceExcludeTax', '', 'Magento_Tax_Model_Config::priceIncludesTax()'],
    [
        'getNumberDetail',
        'Magento\Sales\Model\Order\Shipment\Track',
        'Magento\Shipping\Model\Order\Track::getNumberDetail()',
    ],
    ['getOneBalanceTotal'],
    ['getOptimalCssUrls', 'Magento\Core\Model\Design\Package\Proxy', 'Magento\Framework\View\Asset\Merged'],
    ['getOptimalJsUrls', 'Magento\Core\Model\Design\Package\Proxy', 'Magento\Framework\View\Asset\Merged'],
    ['getOptimalCssUrls', 'Magento\Core\Model\Design\Package', 'Magento\Framework\View\Asset\Merged'],
    ['getOptimalJsUrls', 'Magento\Core\Model\Design\Package', 'Magento\Framework\View\Asset\Merged'],
    ['getOption', 'Magento\Captcha\Helper\Data', 'Magento_Core_Model_Dir::getDir()'],
    ['getOptions', 'Magento\Core\Model\Config'],
    [
        'getOptions',
        'Magento\Core\Model\Design\Source\Design',
        'Magento_Core_Model_Theme::getThemeCollectionOptionArray',
    ],
    ['getOptionValues', 'Magento\Eav\Block\Adminhtml\Attribute\Edit\Options\AbstractOptions'],
    ['getOrderHtml', 'Magento\GoogleAnalytics\Block\Ga'],
    ['getOrderId', 'Magento\Checkout\Block\Onepage\Success'],
    ['getOrderId', 'Magento\Shipping\Block\Tracking\Popup'],
    ['getOriginalHeigh', '', 'getOriginalHeight'],
    ['getOriginalRequest', 'Magento\Framework\App\Request\Http'],
    ['getPackage', 'Magento\Widget\Model\Widget\Instance'],
    ['getPackageTheme', 'Magento\Widget\Model\Widget\Instance', 'getThemeId'],
    ['getPageHandleLabel', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['getPageHandles', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['getPageHandleType', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['getPageTemplateProcessor', 'Magento\Cms\Helper\Data'],
    ['getBlockTemplateProcessor', 'Magento\Cms\Helper\Data'],
    ['getPathAsArray', '', 'direct explode by /'],
    ['getPathFromArray', '', 'direct usage of /'],
    ['getPathVars', 'Magento\Core\Model\Config'],
    ['getParentProductIds', 'Magento\Catalog\Model\ResourceModel\Product'],
    ['getPostMaxSize', 'Magento\Backend\Block\Media\Uploader', 'Magento_File_Size::getPostMaxSize()'],
    ['getPriceFormatted', 'Magento\Customer\Block\Adminhtml\Edit\Tab\View\Sales'],
    ['getPrices', 'Magento\Bundle\Model\Product\Price', 'getTotalPrices()'],
    ['getPricesDependingOnTax', 'Magento\Bundle\Model\Product\Price', 'getTotalPrices()'],
    ['getPrintUrl', 'Magento\Checkout\Block\Onepage\Success'],
    ['getPrintUrl', 'Magento\Sales\Block\Order\Info'],
    ['getProduct', 'Magento\Catalog\Model\Product\Type\AbstractType'],
    ['getProductCollectionAttributes', 'Magento\Catalog\Model\Config'],
    ['getProductCollection', 'Magento\Wishlist\Helper\Data'],
    ['getProductCollection', 'Magento\Wishlist\Model\Wishlist'],
    ['getProductsNotInStoreIds'],
    ['getProfile', 'Magento\Framework\Convert\Container\AbstractContainer'],
    ['getQuoteItem', 'Magento\Catalog\Model\Product\Option\Type\DefaultType'],
    ['getQuoteItemOption', 'Magento\Catalog\Model\Product\Option\Type\DefaultType'],
    ['getQuoteOrdersHtml', 'Magento\GoogleAnalytics\Block\Ga'],
    ['getRefererParamName', 'Magento\Backend\Block\Page\Footer'],
    ['getRelativePath', 'Magento\Core\Model\Theme\Files'],
    ['getRemoveItemUrl', 'Magento\Wishlist\Block\Customer\Sidebar'],
    ['getReorderUrl', 'Magento\Sales\Block\Order\Info'],
    [
        'getResourceConfig',
        'Magento\Framework\Config\Model\Config',
        'Magento_Config_Model_Config_Resource::getResourceConfig',
    ],
    ['getResourceConfig', 'Magento\Core\Model\Config'],
    [
        'getResourceConnectionConfig',
        'Magento\Framework\Config\Model\Config',
        'Magento_Config_Model_Config_Resource::getResourceConnectionConfig',
    ],
    ['getResourceConnectionConfig', 'Magento\Core\Model\Config'],
    [
        'getResourceConnectionModel',
        'Magento\Framework\Config\Model\Config',
        'Magento_Config_Model_Config_Resource::getResourceConnectionModel',
    ],
    ['getResourceConnectionModel', 'Magento\Core\Model\Config'],
    ['getResourceModel', 'Magento\Core\Model\Config'],
    ['getResourceModelClassName', 'Magento\Core\Model\Config'],
    [
        'getResourceTypeConfig',
        'Magento\Framework\Config\Model\Config',
        'Magento_Config_Model_Config_Resource::getResourceTypeConfig',
    ],
    ['getResourceTypeConfig', 'Magento\Core\Model\Config'],
    ['getResTreeJson', 'Magento\User\Block\Role\Tab\Edit', 'getTree'],
    ['getResTreeJson', 'Magento\Backend\Block\Api\Tab\Rolesedit', 'getTree'],
    ['getRouterByRoute', 'Magento\Framework\App\FrontController'],
    ['getRouterByFrontName', 'Magento\Framework\App\FrontController'],
    ['getRouters', 'Magento\Core\Model\Config'],
    ['getRowId', 'Magento\Sales\Block\Adminhtml\Order\Create\Customer\Grid'],
    ['getRowId', 'Magento\Backend\Block\Widget\Grid'],
    ['getSaveTemplateFlag', 'Magento\Newsletter\Model\Queue'],
    ['getSafeStore', 'Magento\Core\Model\StoreManager', 'Magento\Store\Model\StoreManager::getStore'],
    ['getSectionNode', 'Magento\Core\Model\Config'],
    ['getSecure', 'Magento\Backend\Model\UrlInterface', 'isSecure'],
    ['getSecure', 'Magento\Framework\Url', 'isSecure'],
    ['_prepareSessionUrlWithParams', 'Magento\Framework\Url'],
    ['_getQueryParams', 'Magento\Framework\Url'],
    ['_setRouteFrontName', 'Magento\Framework\Url'],
    ['setType', 'Magento\Framework\Url'],
    ['purgeQueryParams', 'Magento\Framework\Url', 'Use setQueryParams([]) instead'],
    ['purgeQueryParams', 'Magento\Framework\Url\QueryParamsResolver', 'Use setQueryParams([]) instead'],
    ['purgeQueryParams', 'Magento\Framework\Url\QueryParamsResolverInterface', 'Use setQueryParams([]) instead'],
    ['_getDefaultActionName', 'Magento\Framework\Url', 'Magento\Framework\UrlInterface::DEFAULT_ACTION_NAME'],
    [
        '_getDefaultControllerName',
        'Magento\Framework\Url',
        'Magento\Framework\UrlInterface::DEFAULT_CONTROLLER_NAME',
    ],
    ['_getDefaultUrlType', 'Magento\Framework\Url', 'Magento\Framework\UrlInterface::DEFAULT_URL_TYPE'],
    ['getSelectionFinalPrice', 'Magento\Bundle\Model\Product\Price'],
    ['getShipId', 'Magento\Shipping\Block\Tracking\Popup'],
    ['getShippingCarrier', 'Magento\Sales\Model\Order', 'Magento\Shipping\Model\CarrierFactory::create'],
    ['getShowTemplateHints', 'Magento\Framework\View\Element\Template'],
    ['getSortedChildBlocks', '', 'getChildNames() + $this->getLayout()->getBlock($name)'],
    ['getSortedChildren', '', 'getChildNames'],
    ['getSortedElements', 'Magento\Framework\Data\Form\Element\Fieldset', 'getElements'],
    ['getStateModelClass', 'Magento\Centinel\Model\Config'],
    ['getStatrupPageUrl'],
    ['getStore', 'Magento\Captcha\Helper\Data'],
    ['getStore', 'Magento\CatalogRule\Block\Adminhtml\Promo\Widget\Chooser\Sku'],
    ['getStoreButtonsHtml', 'Magento\Backend\Block\System\Config\Tabs'],
    ['getStoreCodeFromPath', 'Magento\Framework\App\Request\Http'],
    ['getStoreCurrency', 'Magento\Sales\Model\Order'],
    ['getStoreSelectOptions', 'Magento\Backend\Block\System\Config\Tabs'],
    ['getStores', 'Magento\Eav\Block\Adminhtml\Attribute\Edit\Options\AbstractOptions'],
    ['getSuggestedZeroDate'],
    ['getSuggestionsByQuery'],
    ['getSysTmpDir'],
    ['getTablePrefix', 'Magento\Core\Model\Config'],
    ['getTagsByType', 'Magento\Framework\App\Cache', 'Magento_Cache_Frontend_Decorator_TagScope::getTag()'],
    ['getTagsByType', 'Magento\Framework\App\Cache\Proxy', 'Magento_Cache_Frontend_Decorator_TagScope::getTag()'],
    [
        'getTagsByType',
        'Magento\Framework\App\CacheInterface',
        'Magento_Cache_Frontend_Decorator_TagScope::getTag()',
    ],
    ['getTaxAmount', 'Magento\Quote\Model\Quote\Item\AbstractItem'],
    ['getTaxRatesByProductClass', '', '_getAllRatesByProductClass'],
    ['getThemeVersion', '\Magento\Framework\Config\Theme'],
    ['getAllRatesByProductClass', 'Magento\Tax\Helper\Data'],
    [
        'getTemplateProcessor',
        'Magento\Newsletter\Helper\Data',
        'Use directly model \Magento\Newsletter\Model\Template\Filter',
    ],
    ['getTempVarDir', 'Magento\Core\Model\Config', 'Magento_Core_Model_Dir::getDir()'],
    ['getTestsDir', 'Magento\TestFramework\Bootstrap'],
    ['getTheme', 'Magento\Widget\Model\Widget\Instance'],
    [
        'getThemeOptions',
        'Magento\Core\Model\Design\Source\Design',
        'Magento_Core_Model_Theme::getThemeCollectionOptionArray',
    ],
    ['getTotalModels', 'Magento\Quote\Model\Quote\Address'],
    ['importCustomerAddress', 'Magento\Quote\Model\Quote\Address'],
    ['getTotalModels', 'Magento\Quote\Model\Quote\Config'],
    ['getTrackId', 'Magento\Shipping\Block\Tracking\Popup'],
    ['getTrackingInfoByOrder', 'Magento\Shipping\Block\Tracking\Popup'],
    ['getTrackingInfoByShip', 'Magento\Shipping\Block\Tracking\Popup'],
    ['getTrackingInfoByTrackId', 'Magento\Shipping\Block\Tracking\Popup'],
    ['getTrackingPopUpUrlByOrderId', '', 'getTrackingPopupUrlBySalesModel'],
    ['getTrackingPopUpUrlByShipId', '', 'getTrackingPopupUrlBySalesModel'],
    ['getTrackingPopUpUrlByTrackId', '', 'getTrackingPopupUrlBySalesModel'],
    ['getUnixProcessMemoryUsage', 'Magento\TestFramework\Helper\Memory', 'getRealMemoryUsage'],
    ['getUploadMaxSize', 'Magento\Backend\Block\Media\Uploader', 'Magento_File_Size::getUploadMaxSize()'],
    ['getUrlForReferer', 'Magento\Backend\Block\Page\Footer'],
    ['getValidator', 'Magento\SalesRule\Model\Observer'],
    ['getValidatorData', 'Magento\Core\Model\Session\AbstractSession', 'use _getSessionEnvironment method'],
    ['getValueTable'],
    ['getVarDir', 'Magento\Core\Model\Config', 'Magento_Core_Model_Dir::getDir()'],
    ['getVatValidationUserMessage', 'Magento\Customer\Helper\Data'],
    ['getViewOrderUrl', 'Magento\Checkout\Block\Onepage\Success'],
    ['getWatermarkHeigth', '', 'getWatermarkHeight'],
    ['getWebsite', 'Magento\Captcha\Helper\Data'],
    ['getWidgetConfig', 'Magento\Widget\Model\Widget\Instance', 'getWidgetConfigAsArray'],
    ['getWidgetsRequiredJsFiles', 'Magento\Widget\Model\Widget'],
    ['getWidgetsXml', 'Magento\Widget\Model\Widget', 'getWidgets'],
    ['getWidgetSupportedBlocks', 'Magento\Widget\Model\Widget\Instance'],
    ['getWidgetSupportedTemplatesByBlock', 'Magento\Widget\Model\Widget\Instance'],
    ['getWinProcessMemoryUsage', 'Magento\Test\Helper\Memory', 'getRealMemoryUsage'],
    ['getXmlConfig', 'Magento\Widget\Model\Widget', 'getWidgets'],
    ['getXmlConfig', 'Magento\Persistent\Model\Persistent\Config'],
    ['getXmlElementByType', 'Magento\Widget\Model\Widget', 'getWidgetByClassType'],
    ['hasItems', 'Magento\Wishlist\Helper\Data'],
    ['htmlEscape', '', 'escapeHtml'],
    ['imageAction', 'Magento\Catalog\Controller\Product'],
    ['implodeStreetAddress', 'Magento\Customer\Model\Address\AbstractAddress'],
    ['importFromTextArray'],
    ['init', 'Magento\Framework\App\FrontController'],
    ['init', 'Magento\Core\Model\Config'],
    ['init', 'Magento\Webapi\Controller\Front'],
    ['initCache'],
    ['initControllerRouters', 'Magento\Cms\Controller\Router'],
    [
        'initCurrentStore',
        'Magento\Store\Model\Storage\Db',
        'Magento\Store\Model\StorageFactory::_reinitStores',
    ],
    ['_checkGetStore', 'Magento\Store\Model\Storage\Db'],
    ['_checkCookieStore', 'Magento\Store\Model\Storage\Db'],
    ['_getStoreByGroup', 'Magento\Store\Model\Storage\Db'],
    ['_getStoreByWebsite', 'Magento\Store\Model\Storage\Db'],
    ['initLabels', 'Magento\Catalog\Model\ResourceModel\Eav\Attribute'],
    ['initLayoutMessages', 'Magento\Framework\App\Action\Action'],
    ['initSerializerBlock', 'Magento\Backend\Block\Catalog\Product\Edit\Tab\Ajax\Serializer'],
    ['initSerializerBlock', 'Magento\Backend\Block\Widget\Grid\Serializer'],
    ['insertProductPrice', 'Magento\Catalog\Model\ResourceModel\Product\Attribute\Backend\Tierprice'],
    ['isAbsolutePath'],
    ['isAdmin', 'Magento\Store\Model\Store'],
    ['isAllowedGuestCheckout', 'Magento\Quote\Model\Quote'],
    ['isApplicableToQuote', 'Magento\Payment\Model\Method\AbstractMethod'],
    ['isCheckoutAvailable', 'Magento\Multishipping\Model\Checkout\Type\Multishipping'],
    ['isDevAllowed', 'Magento\Core\Helper\Data', '\Magento\Developer\Helper\Data::isDevAllowed'],
    ['isDirectOutput', 'Magento\Framework\View\Layout'],
    ['isDirectOutput', 'Magento\Framework\View\LayoutInterface'],
    ['isFulAmountCovered'],
    ['isLeyeredNavigationAllowed'],
    ['isLocalConfigLoaded', 'Magento\Core\Model\Config'],
    ['isModuleEnabled', 'Magento\Core\Model\Config', 'Magento_Core_Model_ModuleManager::isEnabled'],
    ['isReadablePopupObject'],
    ['isStaticFilesSigned', 'Magento\Core\Helper\Data'],
    ['isTemplateAllowedForApplication'],
    ['isThemeCompatible', 'Magento\Core\Model\Design\Package', 'Magento_Core_Model_Theme::isThemeCompatible'],
    ['isUserSavingAllowed', 'Magento\User\Model\ResourceModel\User'],
    ['isVerbose', 'Magento\Framework\Shell'],
    ['isWindowsOs', 'Magento\TestFramework\Helper\Memory'],
    ['joinTaxClass', 'Magento\Tax\Helper\Data'],
    ['jsonDecode', 'Magento\Core\Helper\Data', 'Magento\Framework\Json\Helper\Data::jsonDecode'],
    ['jsonEncode', 'Magento\Core\Helper\Data', 'Magento\Framework\Json\Helper\Data::jsonEncode'],
    ['load', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['loadBaseContents', 'Magento\Email\Model\Template'],
    ['loadBase', 'Magento\Core\Model\Config'],
    ['loadByCustomer', 'Magento\Newsletter\Model\ResourceModel\Subscriber', 'loadByCustomerData'],
    ['loadDb', 'Magento\Core\Model\Config'],
    ['loadDiConfiguration', 'Magento\Core\Model\Config'],
    ['loadEventObservers', 'Magento\Core\Model\Config'],
    ['loadLabel', 'Magento\Catalog\Model\ResourceModel\Product\Type\Configurable\Attribute'],
    ['loadModules', 'Magento\Core\Model\Config'],
    ['loadModulesCache', 'Magento\Core\Model\Config'],
    ['loadModulesConfiguration', 'Magento\Core\Model\Config'],
    ['loadParentProductIds', 'Magento\Catalog\Model\Product'],
    ['loadPrices', 'Magento\Catalog\Model\ResourceModel\Product\Type\Configurable\Attribute'],
    ['loadProductPrices', 'Magento\Catalog\Model\ResourceModel\Product\Attribute\Backend\Tierprice'],
    ['logEncryptionKeySave'],
    ['logI' . 'nvitationSave'],
    [
        'map',
        'Magento\Framework\ObjectManager\Config\Mapper\Dom',
        'Magento_ObjectManager_Config_Mapper_Dom::convert',
    ],
    ['mergeFiles', 'Magento\Core\Helper\Data', 'Magento\Framework\View\Asset\MergeStrategyInterface'],
    ['mergeFiles', 'Magento\Core\Model\Design\Package', 'Magento\Framework\View\Asset\MergeStrategyInterface'],
    [
        'mergeFiles',
        'Magento\Core\Model\Design\Package\Proxy',
        'Magento\Framework\View\Asset\MergeStrategyInterface',
    ],
    [
        'mergeFiles',
        'Magento\Core\Model\Design\PackageInterface',
        'Magento\Framework\View\Asset\MergeStrategyInterface',
    ],
    ['order_success_page_view', 'Magento\GoogleAnalytics\Model\Observer'],
    ['orderedAction', 'Magento\Backend\Controller\Report\Product'],
    ['output', 'Magento\Framework\Shell'],
    ['pageHandleExists', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['parseDateTime', 'Magento\Framework\Stdlib\DateTime\DateTime'],
    ['postDispatchMyAccountSave'],
    ['postDispatchSystemImportExportRun'],
    ['prepareAttributesForSave', 'Magento\CatalogImportExport\Model\Import\Product'],
    ['getAffectedProducts', 'Magento\CatalogImportExport\Model\Import\Product'],
    ['getAffectedEntityIds', 'Magento\CatalogImportExport\Model\Import\Product'],
    ['getCategoryWithRoot', 'Magento\CatalogImportExport\Model\Import\Product\CategoryProcessor'],
    ['getCategory', 'Magento\CatalogImportExport\Model\Import\Product\CategoryProcessor'],
    ['prepareGroupPrices', 'Magento\CatalogImportExport\Model\Export\Product'],
    ['prepareTierPrices', 'Magento\CatalogImportExport\Model\Export\Product'],
    ['_populateToUrlGeneration', 'Magento\CatalogImportExport\Model\Export\Product'],
    ['prepareGoogleOptimizerScripts'],
    ['prepareRedirect', 'Magento\Core\Controller\Varien\Exception'],
    ['preparePriceAlertData', 'Magento\ProductAlert\Block\Product\View'],
    ['prepareStockAlertData', 'Magento\ProductAlert\Block\Product\View'],
    ['preprocess', 'Magento\Newsletter\Model\Template'],
    ['processBeacon'],
    ['processBeforeVoid', 'Magento\Payment\Model\Method\AbstractMethod'],
    ['canUseForMultishipping', 'Magento\Payment\Model\Method\AbstractMethod'],
    ['processRequest', 'Magento\Framework\App\Cache'],
    ['processSubst', 'Magento\Store\Model\Store'],
    ['productEventAggregate'],
    ['publishRelatedViewFile', 'Magento\Framework\View\Publisher', '_publishRelatedViewFile'],
    ['push', 'Magento\Catalog\Model\Product\Image'],
    ['regenerateSessionId', 'Magento\Core\Model\Session\AbstractSession'],
    ['reinitialize', 'Magento\TestFramework\Bootstrap', 'Magento_TestFramework_Helper_Bootstrap::reinitialize'],
    ['removeAuthLink', 'Magento\Customer\Block\Account\Link'],
    ['getCustomerName', 'Magento\Customer\Block\Account'],
    ['removeItem', 'Magento\Theme\Block\Html\Head'],
    ['removeCustomerFromSegments'],
    ['removeHandle', 'Magento\Core\Model\Layout\Update', 'Magento\Framework\View\Model\Layout\Merge'],
    ['removeParentCartLink', 'Magento\Checkout\Block\Links'],
    ['removeRegisterLink', 'Magento\Customer\Block\Account\Link'],
    ['renderView', '', 'Magento_Core_Block_Template::_toHtml()'],
    ['revalidateCookie', 'Magento\Core\Model\Session\AbstractSession\Varien'],
    ['runApp', 'Magento\TestFramework\Bootstrap', 'Magento_TestFramework_Helper_Bootstrap::runApp'],
    ['sales_order_afterPlace'],
    ['sales_quote_address_discount_item'],
    ['salesEventConvertQuoteItemToOrderItem', 'Magento\GiftMessage\Model\Observer'],
    ['salesOrderPaymentPlaceEnd'],
    ['saveCache', 'Magento\Core\Model\Config'],
    ['saveConfig', 'Magento\Core\Model\Config'],
    ['saveOptions', 'Magento\Framework\App\Cache'],
    ['saveOptions', 'Magento\Framework\App\Cache\Proxy'],
    ['saveOptions', 'Magento\Framework\App\CacheInterface'],
    ['saveRow__OLD'],
    ['saveSegmentCustomersFromSelect'],
    ['saveUseCache'],
    ['send', 'Magento\Newsletter\Model\Template'],
    ['sendNewPasswordEmail'],
    ['setAnonSuffix'],
    ['setApplyFilters'],
    ['setAttributeDataModelFactory', 'Magento\Eav\Model\Validator\Attribute\Data'],
    ['setAttributeSetExcludeFilter', 'Magento\Eav\Model\ResourceModel\Entity\Attribute\Collection'],
    ['setBlockAlias'],
    ['setConfig', 'Magento\Captcha\Helper\Data'],
    ['setCustomerId', 'Magento\Customer\Model\ResourceModel\Address'],
    ['setDirectOutput', 'Magento\Framework\View\Layout'],
    ['setInstance', 'Magento\TestFramework\Bootstrap', 'Magento_TestFramework_Helper_Bootstrap::setInstance'],
    ['setIsAjaxRequest', 'Magento\Framework\Translate\Inline'],
    ['setNeedUsePriceExcludeTax', '', 'Magento_Tax_Model_Config::setPriceIncludesTax()'],
    ['setOption', 'Magento\Captcha\Helper\Data'],
    ['setOptions', 'Magento\Core\Model\Config'],
    ['setOrderId', 'Magento\Shipping\Block\Tracking\Popup'],
    ['setPackageTheme', 'Magento\Widget\Model\Widget\Instance', 'setThemeId'],
    ['setParentBlock'],
    ['setProduct', 'Magento\Catalog\Model\Product\Type\AbstractType'],
    ['setProfile', 'Magento\Framework\Convert\Container\AbstractContainer'],
    ['setResourceConfig', 'Magento\Framework\App\ResourceConnection'],
    ['setSaveTemplateFlag', 'Magento\Newsletter\Model\Queue'],
    ['setScriptPath'],
    ['setScriptPath', 'Magento\Framework\View\Element\Template'],
    ['setShipId', 'Magento\Shipping\Block\Tracking\Popup'],
    ['setSortElementsByAttribute', 'Magento\Framework\Data\Form\Element\Fieldset'],
    ['setStore', 'Magento\Captcha\Helper\Data'],
    ['setTaxGroupFilter'],
    ['setTrackId', 'Magento\Shipping\Block\Tracking\Popup'],
    ['setVarSubFolders'],
    ['setVerbose', 'Magento\Framework\Shell'],
    ['setWatermarkHeigth', '', 'setWatermarkHeight'],
    ['setWebsite', 'Magento\Captcha\Helper\Data'],
    ['shaCrypt', '', 'Magento_Ogone_Model_Api::getHash'],
    ['shaCryptValidation', '', 'Magento_Ogone_Model_Api::getHash'],
    ['shouldCustomerHaveOneBalance'],
    ['shouldShowOneBalance'],
    ['sortChildren'],
    ['overrideProductThumbnail', 'Magento\Checkout\Block\Cart\Item\Renderer'],
    ['_getThumbnail', 'Magento\Checkout\Block\Cart\Item\Renderer'],
    ['getConfigurableProduct', 'Magento\Checkout\Block\Cart\Item\Renderer\Configurable'],
    ['getProductAttributes', 'Magento\Checkout\Block\Cart\Item\Renderer\Configurable'],
    ['substDistroServerVars', 'Magento\Core\Model\Config'],
    ['superGroupGridOnlyAction', 'Magento\Catalog\Controller\Adminhtml\Product'],
    ['toOptionArray', 'Magento\Cms\Model\ResourceModel\Page\Collection'],
    ['toOptionArray', 'Magento\Sendfriend\Model\Sendfriend'],
    ['truncate', 'Magento\Framework\DB\Adapter\Pdo\Mysql'],
    ['unsetBlock'],
    ['updateCofigurableProductOptions', 'Magento\Weee\Model\Observer', 'updateConfigurableProductOptions'],
    ['updateTable', 'Magento\Core\Model\Resource\Setup'],
    ['urlEscape', '', 'escapeUrl'],
    ['useValidateHttpUserAgent', 'Magento\Core\Model\Session\AbstractSession'],
    ['useValidateHttpVia', 'Magento\Core\Model\Session\AbstractSession'],
    ['useValidateHttpXForwardedFor', 'Magento\Core\Model\Session\AbstractSession'],
    ['useValidateRemoteAddr', 'Magento\Core\Model\Session\AbstractSession'],
    ['validateDataArray', 'Magento\Framework\Convert\Container\AbstractContainer'],
    ['validateFile', 'Magento\Core\Model\Design\Package'],
    ['validateOrder', 'Magento\Checkout\Model\Type\Onepage'],
    ['viewTrackAction', 'Magento\Shipping\Controller\Adminhtml\Order\Shipment'],
    ['_prepareOptionValues', 'Magento\Eav\Block\Adminhtml\Attribute\Edit\Options\AbstractOptions'],
    ['_getOptionValuesCollection', 'Magento\Eav\Block\Adminhtml\Attribute\Edit\Options\AbstractOptions'],
    [
        ' _prepareSystemAttributeOptionValues',
        'Magento\Eav\Block\Adminhtml\Attribute\Edit\Options\AbstractOptions',
    ],
    [
        '_prepareUserDefinedAttributeOptionValues',
        'Magento\Eav\Block\Adminhtml\Attribute\Edit\Options\AbstractOptions',
    ],
    ['getLabelValues', 'Magento\Eav\Block\Adminhtml\Attribute\Edit\Options\AbstractOptions'],
    ['getStoreOptionValues', 'Magento\Eav\Block\Adminhtml\Attribute\Edit\Options\AbstractOptions'],
    ['getAttributeObject', 'Magento\Eav\Block\Adminhtml\Attribute\Edit\Options\AbstractOptions'],
    [
        'substractQtyFromQuotes',
        'Magento\Sales\Model\Observer',
        'Magento_Sales_Model_Observer_Backend_CatalogProductQuote::subtractQtyFromQuotes',
    ],
    [
        'markQuotesRecollectOnCatalogRules',
        'Magento\Sales\Model\Observer',
        'Magento_Sales_Model_Observer_Backend_CatalogPriceRule::dispatch',
    ],
    [
        'catalogProductSaveAfter',
        'Magento\Sales\Model\Observer',
        'Magento_Sales_Model_Observer_Backend_CatalogProductQuote::catalogProductSaveAfter',
    ],
    [
        'catalogProductStatusUpdate',
        'Magento\Sales\Model\Observer',
        'Magento_Sales_Model_Observer_Backend_CatalogProductQuote::catalogProductStatusUpdate',
    ],
    [
        'restrictAdminBillingAgreementUsage',
        'Magento\Sales\Model\Observer',
        'Magento_Sales_Model_Observer_Backend_BillingAgreement::dispatch',
    ],
    [
        'customerSaveAfter',
        'Magento\Sales\Model\Observer',
        'Magento_Sales_Model_Observer_Backend_CustomerQuote::dispatch',
    ],
    ['_getUsers', 'Magento\Backend\Block\Api\Role\Grid\User', 'getUsers'],
    ['_getGridHtml', 'Magento\Backend\Block\Api\Tab\Rolesusers', 'getGridHtml'],
    ['_getSelectedRoles', 'Magento\Backend\Block\Api\User\Edit\Tab\Roles', 'getSelectedRoles'],
    ['_getProduct', 'Magento\Backend\Block\Catalog\Product\Edit\Tab\Super\Config\Matrix', 'getProduct'],
    [
        '_getImageUploadUrl',
        'Magento\Backend\Block\Catalog\Product\Edit\Tab\Super\Config\Matrix',
        'getImageUploadUrl',
    ],
    ['_getProduct', 'Magento\Backend\Block\Catalog\Product\Edit\Tab\Super\Config\Simple', 'getProduct'],
    ['_getProduct', 'Magento\Backend\Block\Catalog\Product\Edit\Tab\Super\Config', 'getProduct'],
    ['_getProduct', 'Magento\Backend\Block\Catalog\Product\Edit\Tab\Super\Settings', 'getProduct'],
    ['_getProduct', 'Magento\Backend\Block\Catalog\Product\Edit\Tab\Crosssell', 'getProduct'],
    ['_getProduct', 'Magento\Backend\Block\Catalog\Product\Edit\Tab\Related', 'getProduct'],
    ['_getProduct', 'Magento\Backend\Block\Catalog\Product\Edit\Tab\Upsell', 'getProduct'],
    [
        '_renderCellTemplate',
        'Magento\Backend\Block\System\Config\Form\Field\Array\AbstractArray',
        'renderCellTemplate',
    ],
    ['_showSingle', 'Magento\Bundle\Block\Catalog\Product\View\Type\Bundle\Option', 'showSingle'],
    ['_getDefaultValues', 'Magento\Bundle\Block\Catalog\Product\View\Type\Bundle\Option', 'getDefaultValues'],
    ['_isSelected', 'Magento\Bundle\Block\Catalog\Product\View\Type\Bundle\Option', 'isSelected'],
    [
        '_getProduct',
        'Magento\Catalog\Block\Product\Configurable\AssociatedSelector\Backend\Grid\ColumnSet',
        'getProduct',
    ],
    ['_getProduct', 'Magento\CatalogInventory\Block\Stockqty\AbstractStockqty', 'getProduct'],
    ['_getProduct', 'Magento\CatalogInventory\Block\Qtyincrements', 'getProduct'],
    ['_getPageTrackingCode', 'Magento\GoogleAnalytics\Block\Ga', 'getPageTrackingCode'],
    ['_getOrdersTrackingCode', 'Magento\GoogleAnalytics\Block\Ga', 'getOrdersTrackingCode'],
    ['_toJson', 'Magento\GoogleShopping\Block\Adminhtml\Types\Edit\Attributes', 'jsonFormat'],
    ['_getConfig', 'Magento\GoogleShopping\Controller\Adminhtml\Googleshopping\Items'],
    ['_getSession', 'Magento\GoogleShopping\Model\MassOperations'],
    ['_getStore', 'Magento\GoogleShopping\Block\Adminhtml\Items\Item'],
    ['_createAttribute', 'Magento\GoogleShopping\Model\Type'],
    ['_prepareModelName', 'Magento\GoogleShopping\Model\Type'],
    ['_getUsers', 'Magento\User\Block\Role\Grid\User', 'getUsers'],
    ['_getGridHtml', 'Magento\User\Block\Role\Tab\Users', 'getGridHtml'],
    ['_getSelectedRoles', 'Magento\User\Block\User\Edit\Tab\Roles', 'getSelectedRoles'],
    ['_prepareSelect', 'Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection'],
    ['_prepareSelect', 'Magento\Framework\Data\Collection\AbstractDb'],
    ['_createOrderFromAddress', 'Magento\Checkout\Model\Type\AbstractType'],
    ['_addLoadAttributesSelectFields', 'Magento\Catalog\Model\ResourceModel\AbstractResource'],
    ['attributeSelectFields', 'Magento\Catalog\Model\ResourceModel\Helper'],
    ['_addLoadAttributesSelectFields', 'Magento\Eav\Model\Entity\AbstractEntity'],
    ['attributeSelectFields', 'Magento\Eav\Model\ResourceModel\Helper'],
    ['prepareEavAttributeValue', 'Magento\Eav\Model\ResourceModel\Helper'],
    ['prepareColumn', 'Magento\Eav\Model\ResourceModel\Helper'],
    ['getInsertFromSelectUsingAnalytic', 'Magento\Eav\Model\ResourceModel\Helper'],
    ['setOneRowLimit', 'Magento\Eav\Model\ResourceModel\Helper'],
    ['getCarrierByCode', 'Magento\Shipping\Model\Config', 'Magento\Shipping\Model\CarrierFactory::create||get'],
    ['getCarrierInstance', 'Magento\Shipping\Model\Config', 'Magento\Shipping\Model\CarrierFactory::create||get'],
    ['getCastToIntExpression', 'Magento\Eav\Model\ResourceModel\Helper'],
    ['_initMetaTags', 'Magento\Theme\Block\Html\Head'],
    ['addMetaTag', 'Magento\Theme\Block\Html\Head'],
    ['getDefaultMetaTags', 'Magento\Theme\Block\Html\Head'],
    ['getDefaultTemplates', 'Magento\Email\Model\Template'],
    [
        'getDefaultTemplatesAsOptionsArray',
        'Magento\Email\Model\Template',
        'Magento_Adminhtml_Block_System_Email_Template_Edit::_getDefaultTemplatesAsOptionsArray',
    ],
    ['getMetaTags', 'Magento\Theme\Block\Html\Head'],
    ['getMetaTagHtml', 'Magento\Theme\Block\Html\Head'],
    ['addLink', 'Magento\Sales\Block\Order\Info'],
    ['checkLinks', 'Magento\Sales\Block\Order\Info'],
    ['getLinks', 'Magento\Sales\Block\Order\Info'],
    ['validate', 'Magento\Core\Model\Session\AbstractSession'],
    ['_validate', 'Magento\Core\Model\Session\AbstractSession'],
    ['_getSessionEnvironment', 'Magento\Core\Model\Session\AbstractSession'],
    ['getValidateHttpUserAgentSkip', 'Magento\Core\Model\Session\AbstractSession'],
    ['addProductAttributes', 'Magento\SalesRule\Model\Observer'],
    ['_helper', 'Magento\Catalog\Model\Product\Type\AbstractType'],
    ['getHelper', 'Magento\Catalog\Model\ResourceModel\Product\Type\Configurable\Attribute\Collection'],
    ['getHelper', 'Magento\Cms\Model\Wysiwyg\Images\Storage'],
    ['getCatalogHelper', 'Magento\Backend\Block\Catalog\Category\Tabs'],
    ['_getSession', 'Magento\Centinel\Model\Service'],
    ['_getValidationStateModel', 'Magento\Centinel\Model\Service'],
    ['_getApi', 'Magento\Ogone\Block\Placeform'],
    [
        '_getResource',
        'Magento\Weee\Model\Attribute\Backend\Weee\Tax',
        'Magento_Weee_Model_Attribute_Backend_Weee_Tax::$_attributeTax',
    ],
    ['getCouponMassGenerator', 'Magento\SalesRule\Model\Rule', 'Magento\SalesRule\Model\Coupon\Massgenerator'],
    ['_getHelper', 'Magento\Captcha\Model\DefaultModel'],
    ['getSession', 'Magento\Captcha\Model\DefaultModel'],
    ['_getBackendSession', 'Magento\Captcha\Model\Observer'],
    ['_resetAttempt', 'Magento\Captcha\Model\Observer'],
    ['getProductStatusModel', 'Magento\CatalogInventory\Model\Stock\Status'],
    ['getStorage', 'Magento\Cms\Helper\Wysiwyg\Images'],
    ['_getSession', 'Magento\Review\Helper\Action\Pager'],
    ['expandSource', 'Magento\ImportExport\Model\Import'],
    ['_getProductType', 'Magento\Backend\Block\Catalog\Product\Edit\Tab\Super\Config\Matrix'],
    ['_getProductType', 'Magento\Backend\Block\Catalog\Product\Edit\Tab\Super\Config'],
    ['_getSession', 'Magento\Sales\Model\AdminOrder'],
    ['setIsSerializable', 'Magento\Framework\App\State'],
    ['getIsSerializable', 'Magento\Framework\App\State'],
    ['_getInventoryItemResource', 'Magento\Reports\Model\ResourceModel\Product\Lowstock\Collection'],
    ['turnOnReadCommittedMode', 'Magento\Backup\Model\ResourceModel\Db'],
    ['turnOnSerializableMode', 'Magento\Backup\Model\ResourceModel\Db', 'prepareTransactionIsolationLevel'],
    ['turnOnMaintenanceMode', 'Magento\Backup\Helper\Data', 'Magento\Framework\App\MaintenanceMode::set'],
    ['turnOffMaintenanceMode', 'Magento\Backup\Helper\Data', 'Magento\Framework\App\MaintenanceMode::set'],
    ['getMaintenanceFlagFilePath', 'Magento\Backup\Helper\Data'],
    ['_getResourceModel', '\Magento\Webapi\Model\Source\Acl\Role', '$this->_resource'],
    ['_getSession', '\Magento\GiftMessage\Model\Save', '$this->_session'],
    ['run', '\Magento\Framework\AppInterface'],
    ['setModuleDir', 'Magento\Framework\Module\Dir\Reader'],
    ['setModuleDir', 'Magento\Core\Model\Config'],
    ['getAreaConfig', 'Magento\Core\Model\Config'],
    ['getRandomString', '\Magento\Core\Helper\Data'],
    ['getHttpHost', '\Magento\Core\Helper\Http'],
    ['getHttpUserAgent', '\Magento\Core\Helper\Http'],
    ['getHttpUserAcceptLanguage', '\Magento\Core\Helper\Http'],
    ['getHttpUserAcceptCharset', '\Magento\Core\Helper\Http'],
    ['getHttpUserAcceptReferer', '\Magento\Core\Helper\Http'],
    ['getHttpUserRemoteAddr', '\Magento\Core\Helper\Http'],
    ['getHttpUserServerAddr', '\Magento\Core\Helper\Http'],
    ['getEncryptor', '\Magento\Core\Helper\Data', '\Magento\Framework\Encryption\Encryptor'],
    ['encrypt', '\Magento\Core\Helper\Data'],
    ['decrypt', '\Magento\Core\Helper\Data'],
    ['validateKey', '\Magento\Core\Helper\Data', '\Magento\Framework\Encryption\EncryptorInterface'],
    ['validateHash', '\Magento\Core\Helper\Data'],
    ['getHash', '\Magento\Core\Helper\Data'],
    ['escapeHtml', '\Magento\Framework\App\Helper\AbstractHelper', '\Magento\Framework\Escaper::escapeHtml'],
    ['escapeUrl', '\Magento\Framework\App\Helper\AbstractHelper', '\Magento\Framework\Escaper::escapeUrl'],
    ['jsQuoteEscape', '\Magento\Framework\App\Helper\AbstractHelper', '\Magento\Framework\Escaper::escapeJsQuote'],
    ['quoteEscape', '\Magento\Framework\App\Helper\AbstractHelper', '\Magento\Framework\Escaper::escapeQuote'],
    ['removeTags', '\Magento\Framework\App\Helper\AbstractHelper', '\Magento\Framework\Filter\FilterManager'],
    ['removeAccents', '\Magento\Core\Helper\Data', '\Magento\Framework\Filter\FilterManager'],
    ['splitWords', '\Magento\Core\Helper\String', '\Magento\Framework\Filter\FilterManager'],
    ['strlen', '\Magento\Core\Helper\String', '\Magento\Framework\Stdlib\String::strlen'],
    ['substr', '\Magento\Core\Helper\String', '\Magento\Framework\Stdlib\String::substr'],
    ['strrev', '\Magento\Core\Helper\String', '\Magento\Framework\Stdlib\String::strrev'],
    ['cleanString', '\Magento\Core\Helper\String', '\Magento\Framework\Stdlib\String::cleanString'],
    ['strpos', '\Magento\Core\Helper\String', '\Magento\Framework\Stdlib\String::strpos'],
    ['strSplit', '\Magento\Core\Helper\String', '\Magento\Framework\Stdlib\String::split'],
    ['splitInjection', '\Magento\Core\Helper\String', '\Magento\Framework\Stdlib\String::splitInjection'],
    ['truncate', '\Magento\Core\Helper\String', '\Magento\Framework\Filter\Truncate'],
    ['ksortMultibyte', '\Magento\Core\Helper\String'],
    ['uc_words'],
    ['is_empty_date'],
    ['now'],
    ['uniqHash', '\Magento\Core\Helper\Data', '\Magento\Framework\Math\Random::getUniqueHash'],
    [
        'getMerchantCountryCode',
        '\Magento\Core\Helper\Data',
        '\Magento\Customer\Helper\Data::getMerchantCountryCode',
    ],
    ['getMerchantVatNumber', '\Magento\Core\Helper\Data', '\Magento\Customer\Helper\Data::getMerchantVatNumber'],
    ['isCountryInEU', '\Magento\Core\Helper\Data', '\Magento\Customer\Helper\Data::isCountryInEU'],
    ['getCurrentCustomer', '\Magento\Core\Helper\Data'],
    ['getCustomerName', '\Magento\Core\Helper\Data'],
    ['_createForm', '\Magento\Core\Helper\Data'],
    ['assocToXml', '\Magento\Core\Helper\Data', '\Magento\Framework\Convert\ConvertArray::assocToXml'],
    ['xmlToAssoc', '\Magento\Core\Helper\Data', '\Magento\Framework\Convert\Xml::xmlToAssoc'],
    ['checkLfiProtection', '\Magento\Core\Helper\Data', '\Magento\Framework\Filesystem::checkLfiProtection'],
    [
        'getProtectedFileExtensions',
        '\Magento\Core\Helper\Data',
        '\Magento\MediaStorage\Model\File\Validator\NotProtectedExtension::getProtectedFileExtensions',
    ],
    ['getStoreId', '\Magento\Core\Helper\Data'],
    ['getExactDivision', '\Magento\Core\Helper\Data', '\Magento\Framework\Math\Division::getExactDivision'],
    ['getPublicFilesValidPath', '\Magento\Core\Helper\Data'],
    ['getViewConfig', '\Magento\Core\Model\View\Config', 'get'],
    ['_getSession', '\Magento\Catalog\Helper\Product\Compare', '$this->_catalogSession'],
    ['getSitemapValidPaths', '\Magento\Catalog\Helper\Catalog', '\Magento\Sitemap\Helper\Data::getValidPaths'],
    ['getEnginePool', '\Magento\Framework\View\Element\Template\Context', 'getEngineFactory'],
    ['getHtml', 'Magento\Framework\View\Element\Messages'],
    ['is_dir_writeable'],
    ['destruct'],
    ['mageDebugBacktrace'],
    ['mageDelTree'],
    ['mageParseCsv'],
    [
        'getHttpAuthCredentials',
        '\Magento\Core\Helper\Http',
        '\Magento\Framework\HTTP\Authentication::getCredentials',
    ],
    [
        'failHttpAuthentication',
        '\Magento\Core\Helper\Http',
        '\Magento\Framework\HTTP\Authentication::setAuthenticationFailed',
    ],
    ['getRequestUri', '\Magento\Core\Helper\Http', '\Magento\Framework\HTTP\Header::getRequestUri'],
    [
        'getRequiredAgreementIds',
        '\Magento\Checkout\Helper\Data',
        '\Magento\CheckoutAgreements\Model\AgreementsProvider::getRequiredAgreementIds',
    ],
    ['validateIpAddr', '\Magento\Core\Helper\Http', '\Laminas\Validator\Ip::isValid'],
    [
        'getRemoteAddr',
        '\Magento\Core\Helper\Http',
        'Magento\Framework\HTTP\PhpEnvironment\RemoteAddress::getRemoteAddress',
    ],
    [
        'getServerAddr',
        '\Magento\Core\Helper\Http',
        'Magento\Framework\HTTP\PhpEnvironment\ServerAddress::getServerAddress',
    ],
    [
        'formatDate',
        'Magento\Framework\Model\ResourceModel\AbstractResource',
        '\Magento\Framework\Stdlib\DateTime::formatDate',
    ],
    [
        'mktime',
        'Magento\Framework\Model\ResourceModel\AbstractResource',
        '\Magento\Framework\Stdlib\DateTime::toTimestamp',
    ],
    ['getPlainTemplateMode', '\Magento\Email\Model\Template\Filter'],
    ['getMessage', '\Magento\Core\Model\Session\Context', 'getMessageFactory'],
    ['getMessageFactory', '\Magento\Core\Model\Session\Context', 'getMessagesFactory'],
    ['copyFieldsetToTarget', 'Magento\Core\Helper\Data', 'Magento\Framework\DataObject\Copy::copyFieldsetToTarget'],
    ['formatDate', '\Magento\Core\Helper\Data', '\Magento\Core\Model\Locale::formatDate'],
    ['formatTime', '\Magento\Core\Helper\Data', '\Magento\Core\Model\Locale::formatTime'],
    ['getCustomer', 'Magento\Backend\Model\Session\Quote', 'Magento\Backend\Model\Session\Quote::getCustomerId'],
    ['setCustomer', 'Magento\Backend\Model\Session\Quote', 'Magento\Backend\Model\Session\Quote::setCustomerId'],
    [
        '_getConfigTimezone',
        '\Magento\Framework\Stdlib\DateTime\DateTime',
        '\Magento\Core\Model\Locale::getConfigTimezone',
    ],
    ['_filterPostData', '\Magento\Checkout\Controller\Onepage'],
    ['_filterPostData', '\Magento\Customer\Controller\Account'],
    ['_filterPostData', '\Magento\Customer\Controller\Adminhtml\Index'],
    ['setRedirectWithCookieCheck', '\Magento\Core\Controller\Varien\Action'],
    ['noCookiesAction', '\Magento\Core\Controller\Varien\Action'],
    ['norouteAction', '\Magento\Core\Controller\Varien\Action'],
    ['getActionMethodName', '\Magento\Core\Controller\Varien\Action'],
    [
        'initLayoutMessages',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\View\Layout::initMessages',
    ],
    [
        '_initLayoutMessages',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\View\Layout::initMessages',
    ],
    ['preDispatch', '\Magento\Core\Controller\Varien\Action'],
    ['postDispatch', '\Magento\Core\Controller\Varien\Action'],
    ['hasAction', '\Magento\Core\Controller\Varien\Action'],
    [
        '_startSession',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Core\App\Action\Plugin\Session::aroundDispatch',
    ],
    [
        '_redirectSuccess',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\App\Response\RedirectInterface::success',
    ],
    [
        '_redirectUrl',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\App\Response\RedirectInterface::redirect',
    ],
    [
        '_redirectError',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\App\Response\RedirectInterface::error',
    ],
    ['_redirectReferer', '\Magento\Core\Controller\Varien\Action'],
    [
        '_validateFormKey',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Core\App\Action\FormKeyValidator::validate',
    ],
    [
        '_getRefererUrl',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\App\Response\RedirectInterface::getRefererUrl',
    ],
    ['_isUrlInternal', '\Magento\Core\Controller\Varien\Action'],
    [
        '_filterDateTime',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\Stdlib\DateTime\Filter\DateTime::filter',
    ],
    [
        '_filterDates',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\Stdlib\DateTime\Filter\Date::filter',
    ],
    ['_initDesign', '\Magento\Core\Controller\Varien\Action', '\Magento\Core\Model\DesignLoader::load'],
    ['_firePreDispatchEvents', '\Magento\Core\Controller\Varien\Action'],
    ['_prepareDownloadResponse', '\Magento\Core\Controller\Varien\Action'],
    ['_title', '\Magento\Core\Controller\Varien\Action', '\Magento\Framework\App\Action\Title::add'],
    ['_renderTitles', '\Magento\Core\Controller\Varien\Action'],
    ['getFlag', '\Magento\Core\Controller\Varien\Action', '\Magento\Framework\App\ActionFlag::get'],
    ['setFlag', '\Magento\Core\Controller\Varien\Action', '\Magento\Framework\App\ActionFlag::set'],
    ['loadLayout', '\Magento\Core\Controller\Varien\Action', '\Magento\Framework\App\ViewInterface::'],
    [
        'addPageLayoutHandles',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\App\ViewInterface::loadLayout',
    ],
    [
        'getDefaultLayoutHandle',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\App\ViewInterface::getDefaultLayoutHandle',
    ],
    [
        'generateLayoutXml',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\App\ViewInterface::generateLayoutXml',
    ],
    [
        'renderLayout',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\App\ViewInterface::renderLayout',
    ],
    ['getLayout', '\Magento\Core\Controller\Varien\Action', '\Magento\Framework\App\ViewInterface::getLayout'],
    [
        'loadLayoutUpdates',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\App\ViewInterface::loadLayoutUpdates',
    ],
    [
        'generateLayoutBlocks',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\App\ViewInterface::generateLayoutBlocks',
    ],
    [
        'addActionLayoutHandles',
        '\Magento\Core\Controller\Varien\Action',
        '\Magento\Framework\App\ViewInterface::addActionLayoutHandles',
    ],
    ['getTranslator', '\Magento\Framework\App\Helper\Context'],
    ['getTranslator', '\Magento\Core\Helper\Data'],
    ['_loadCache', '\Magento\Framework\App\Helper\AbstractHelper'],
    ['_saveCache', '\Magento\Framework\App\Helper\AbstractHelper'],
    ['_removeCache', '\Magento\Framework\App\Helper\AbstractHelper'],
    ['_cleanCache', '\Magento\Framework\App\Helper\AbstractHelper'],
    [
        'changeQuoteCustomerGroupId',
        '\Magento\Sales\Model\Observer',
        '\Magento\Quote\Model\Observer\Frontend\Quote\Address\CollectTotals::dispatch',
    ],
    [
        'getEncryptedSessionId',
        '\Magento\Core\Model\Session\AbstractSession',
        '\Magento\Core\Model\Session\AbstractSession::getSessionId',
    ],
    [
        'getSessionIdQueryParam',
        '\Magento\Core\Model\Session\AbstractSession',
        '\Magento\Core\Model\Session\SidResolver::getSessionIdQueryParam',
    ],
    ['getCookie', '\Magento\Core\Model\Session\AbstractSession'],
    [
        'unsetAll',
        '\Magento\Core\Model\Session\AbstractSession',
        '\Magento\Core\Model\Session\AbstractSession::clearStorage',
    ],
    [
        'clear',
        '\Magento\Core\Model\Session\AbstractSession',
        '\Magento\Core\Model\Session\AbstractSession::clearStorage',
    ],
    ['delete', '\Magento\Framework\Stdlib\Cookie', '\Magento\Framework\Stdlib\Cookie::set'],
    ['setSkipSessionIdFlag', '\Magento\Core\Model\Session\AbstractSession'],
    ['getSkipSessionIdFlag', '\Magento\Core\Model\Session\AbstractSession'],
    ['getConfigDomain', '\Magento\Framework\Stdlib\Cookie'],
    ['getDomain', '\Magento\Framework\Stdlib\Cookie'],
    ['getDefaultLifetime', '\Magento\Framework\Stdlib\Cookie'],
    ['getPath', '\Magento\Framework\Stdlib\Cookie'],
    ['getHttponly', '\Magento\Framework\Stdlib\Cookie'],
    ['isSecure', '\Magento\Framework\Stdlib\Cookie'],
    ['getStore', '\Magento\Framework\Stdlib\Cookie'],
    ['setStore', '\Magento\Framework\Stdlib\Cookie'],
    ['setLifetime', '\Magento\Framework\Stdlib\Cookie'],
    ['_getResponse', '\Magento\Framework\Stdlib\Cookie'],
    ['_getRequest', '\Magento\Framework\Stdlib\Cookie'],
    ['init', '\Magento\Core\Model\Session\AbstractSession', '\Magento\Core\Model\Session\AbstractSession::start'],
    ['getCacheLimiter', '\Magento\Core\Model\Session\Context', '\Magento\Core\Model\Session\Config'],
    ['getSaveMethod', '\Magento\Core\Model\Session\Context', '\Magento\Core\Model\Session\Config'],
    ['getAppState', '\Magento\Core\Model\Session\Context'],
    ['getValidator', '\Magento\Core\Model\Session\Context'],
    ['getDir', '\Magento\Core\Model\Session\Context', '\Magento\Core\Model\Session\Config'],
    ['getSavePath', '\Magento\Core\Model\Session\Context', '\Magento\Core\Model\Session\Config'],
    ['getEventManager', '\Magento\Core\Model\Session\Context'],
    ['getLogger', '\Magento\Core\Model\Session\Context'],
    ['getStoreConfig', '\Magento\Core\Model\Session\Context'],
    ['getStoreManager', '\Magento\Core\Model\Session\Context'],
    ['getRequest', '\Magento\Core\Model\Session\Context'],
    ['getSessionSavePath', '\Magento\Core\Model\Session\AbstractSession', '\Magento\Core\Model\Session\Config'],
    ['getCookie', 'Magento\Framework\View\Element\Js\Cookie'],
    ['setCopyright', 'Magento\Theme\Block\Html\Footer'],
    ['setLogo', 'Magento\Theme\Block\Html\Header'],
    ['isHomePage', 'Magento\Theme\Block\Html\Header', 'Magento\Theme\Block\Html\Header\Logo::isHomePage'],
    ['getLogoSrc', 'Magento\Theme\Block\Html\Header', 'Magento\Theme\Block\Html\Header\Logo::getLogoSrc'],
    ['getLogoAlt', 'Magento\Theme\Block\Html\Header', 'Magento\Theme\Block\Html\Header\Logo::getLogoAlt'],
    ['_getLogoUrl', 'Magento\Theme\Block\Html\Header', 'Magento\Theme\Block\Html\Header\Logo::_getLogoUrl'],
    ['_isFile', 'Magento\Theme\Block\Html\Header', 'Magento\Theme\Block\Html\Header\Logo::_isFile'],
    ['_beforeCacheUrl', 'Magento\Framework\View\Element\AbstractBlock'],
    ['_afterCacheUrl', 'Magento\Framework\View\Element\AbstractBlock'],
    ['_getGroupFor', 'Magento\Framework\View\Asset\GroupedCollection', 'getGroupFor'],
    ['_composeMergedContent', 'Magento\Framework\View\Asset\MergeStrategy\Direct', 'composeMergedContent'],
    ['_initialize', 'Magento\Framework\View\Asset\Merged', 'initialize'],
    ['_getMergedAsset', 'Magento\Framework\View\Asset\Merged', 'getMergedAsset'],
    ['_getPublicFilesToMerge', 'Magento\Framework\View\Asset\Merged', 'getPublicFilesToMerge'],
    ['_getMergedFilePath', 'Magento\Framework\View\Asset\Merged', 'getMergedFilePath'],
    ['_process', 'Magento\Framework\View\Asset\Minified', 'process'],
    ['_getMinifier', 'Magento\Framework\View\Asset\MinifyService', 'getMinifier'],
    ['_isEnabled', 'Magento\Framework\View\Asset\MinifyService', 'isEnabled'],
    ['_getAdapter', 'Magento\Framework\View\Asset\MinifyService', 'getAdapter'],
    ['getShowPerPage', 'Magento\Theme\Block\Html\Pager', 'isShowPerPage'],
    ['getPackageByUserAgent', '\Magento\Core\Model\View\Design'],
    ['setFrameTags', 'Magento\Framework\View\Element\AbstractBlock'],
    ['getMessagesBlock', 'Magento\Framework\View\Element\AbstractBlock'],
    ['setMessagesBlock', 'Magento\Framework\View\Element\AbstractBlock'],
    ['getUrlEncoded', 'Magento\Framework\View\Element\AbstractBlock'],
    ['getUrlBase64', 'Magento\Framework\View\Element\AbstractBlock'],
    ['getMessagesBlock', 'Magento\MultipleWishlist\Block\Info'],
    ['helper', 'Magento\Framework\View\Element\AbstractBlock'],
    ['getDataHelperName', 'Magento\Backend\Block\Dashboard\AbstractDashboard'],
    ['setDataHelperName', 'Magento\Backend\Block\Dashboard\AbstractDashboard'],
    ['addStoresToCollection', '\Magento\Review\Model\ResourceModel\Rating\Collection'],
    ['getSessionSaveMethod', '\Magento\Core\Model\Session\AbstractSession', '\Magento\Core\Model\Session\Config'],
    ['setSessionName', '\Magento\Core\Model\Session\AbstractSession', 'setName'],
    ['getCode', '\Magento\Framework\Message\AbstractMessage', 'getText'],
    ['setCode', '\Magento\Framework\Message\AbstractMessage', 'setText'],
    ['setClass', '\Magento\Framework\Message\AbstractMessage'],
    ['setMethod', '\Magento\Framework\Message\AbstractMessage'],
    ['add', '\Magento\Framework\Message\Collection', 'addMessage'],
    ['count', '\Magento\Framework\Message\Collection', 'getCountByType'],
    ['error', '\Magento\Framework\Message\Factory', 'create'],
    ['warning', '\Magento\Framework\Message\Factory', 'create'],
    ['success', '\Magento\Framework\Message\Factory', 'create'],
    ['notice', '\Magento\Framework\Message\Factory', 'create'],
    ['getDisplayMode', '\Magento\Catalog\Model\Session'],
    ['setEscapeMessageFlag', 'Magento\Framework\View\Block\Messages'],
    ['shouldEscapeMessage', 'Magento\Framework\View\Block\Messages'],
    ['isPathInDirectory', 'Magento\Framework\Filesystem\Directory\ReadInterface'],
    ['isSuper', '\Magento\Catalog\Model\Product'],
    ['getCustomerTaxClassWithDefault', '\Magento\Tax\Model\Calculation\Rule'],
    ['getProductTaxClassWithDefault', '\Magento\Tax\Model\Calculation\Rule'],
    ['isSuperGroup', '\Magento\Catalog\Model\Product'],
    ['isGrouped', '\Magento\Catalog\Model\Product'],
    ['isSuperConfig', '\Magento\Catalog\Model\Product'],
    ['getGroupedLinkCollection', '\Magento\Catalog\Model\Product'],
    ['duplicate', '\Magento\Catalog\Model\Product', '\Magento\Catalog\Model\Product\Copier::copy'],
    ['useGroupedLinks', '\Magento\Catalog\Model\Product\Link'],
    ['saveGroupedLinks', '\Magento\Catalog\Model\Product\Link'],
    [
        '_initProductSave',
        '\Magento\Catalog\Controller\Adminhtml\Product',
        '\Magento\Catalog\Controller\Adminhtml\Product\Initialization\Helper::initialize',
    ],
    ['superGroupAction', '\Magento\Catalog\Controller\Adminhtml\Product'],
    [
        'superGroupPopupAction',
        '\Magento\Catalog\Controller\Adminhtml\Product',
        '\Magento\GroupedProduct\Controller\Adminhtml\Edit::popupAction',
    ],
    [
        'prepareProductSave',
        '\Magento\Downloadable\Model\Observer',
        '\Magento\Downloadable\Controller\Adminhtml\Product\Initialization\Helper\Plugin\Downloadable::afterInitialize',
    ],
    [
        'duplicateProduct',
        '\Magento\Downloadable\Model\Observer',
        '\Magento\Downloadable\Model\Product\CopyConstructor\Downloadable::build',
    ],
    [
        'catalogProductPrepareSave',
        '\Magento\PricePermissions\Model\Observer',
        '\Magento\PricePermissions\Controller\Adminhtml\Product\Initialization\Helper\Plugin\PricePermissions::' .
        'afterInitialize',
    ],
    [
        'getGroupedOptions',
        '\Magento\Catalog\Helper\Product\Configuration',
        '\Magento\GroupedProduct\Helper\Product\Configuration\Plugin\Grouped::aroundGetOptions',
    ],
    ['getTrackingAjaxUrl', 'Magento\Shipping\Helper\Data'],
    ['isFreeMethod', 'Magento\Shipping\Helper\Data'],
    ['_initOrder', 'Magento\Shipping\Controller\Tracking'],
    ['ajaxAction', 'Magento\Shipping\Controller\Tracking'],
    ['_getTracksCollection', 'Magento\Shipping\Controller\Tracking'],
    ['getAddToWishlistUrl', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getAddToWishlistUrl', 'Magento\Catalog\Helper\Product\Compare'],
    ['getAddToWishlistUrl', 'Magento\Wishlist\Block\AbstractBlock'],
    ['getAddUrlWithParams', 'Magento\Wishlist\Helper\Data'],
    ['getMoveFromCartUrl', 'Magento\Wishlist\Helper\Data'],
    ['getAddUrl', 'Magento\Wishlist\Helper\Data'],
    ['getRemoveUrl', 'Magento\Wishlist\Helper\Data'],
    ['getUpdateUrl', 'Magento\Wishlist\Helper\Data'],
    ['getItemRemoveUrl', 'Magento\Wishlist\Block\AbstractBlock'],
    ['_getUrlParams', 'Magento\Catalog\Helper\Product\Compare'],
    ['_getInitialXml', 'Magento\Framework\Config\Theme'],
    ['_getIdAttributes', 'Magento\Framework\Config\Theme'],
    ['_getSession', 'Magento\CatalogSearch\Controller\Result'],
    ['addPriceBlockType', 'Magento\Rss\Block\Catalog\AbstractCatalog'],
    ['getAttributeDisabledTypes', 'Magento\Catalog\Helper\Data'],
    [
        '_processArrayNode',
        'Magento\Framework\ObjectManager\Config\Mapper\Dom',
        'Magento\Framework\Data\Argument\Interpreter\ArrayType',
    ],
    [
        '_processValueNode',
        'Magento\Framework\ObjectManager\Config\Mapper\Dom',
        'Magento\Framework\Data\Argument\Interpreter\Composite',
    ],
    ['canSkipFilePublication', 'Magento\Framework\View\Publisher'],
    ['_getExtension', 'Magento\Framework\View\Publisher'],
    [
        '_buildPublishedFilePath',
        'Magento\Framework\View\Publisher',
        'Magento\Framework\View\Publisher\FileInterface',
    ],
    [
        '_buildPublicViewRedundantFilename',
        'Magento\Framework\View\Publisher',
        'Magento\Framework\View\Publisher\File',
    ],
    [
        '_buildPublicViewSufficientFilename',
        'Magento\Framework\View\Publisher',
        'Magento\Framework\View\Publisher\File',
    ],
    [
        '_buildPublicViewFilename',
        'Magento\Framework\View\Publisher',
        'Magento\Framework\View\Publisher\FileInterface',
    ],
    ['cleanAction', 'Magento\PageCache\Controller\Adminhtml\PageCache'],
    ['_isAllowed', 'Magento\PageCache\Controller\Adminhtml\PageCache'],
    ['_downloadFileAction', 'Magento\Sales\Controller\Download', '\Magento\Sales\Model\Download::downloadFile'],
    [
        '_processDatabaseFile',
        'Magento\Sales\Controller\Download',
        '\Magento\Sales\Model\Download::_processDatabaseFile',
    ],
    ['useDbCompatibleMode', 'Magento\Core\Helper\Data'],
    ['getFlag', 'Magento\Catalog\Helper\Product\Flat'],
    ['isBuilt', 'Magento\Catalog\Helper\Product\Flat'],
    ['getIndexerIds', 'Magento\Indexer\Model\Config'],
    ['refreshEnabledIndex', 'Magento\Catalog\Model\ResourceModel\Product'],
    ['refreshIndex', 'Magento\Catalog\Model\ResourceModel\Product'],
    ['getProductStatusEnabled', 'Magento\CatalogInventory\Model\Stock\Status'],
    ['getProductStatus', 'Magento\CatalogInventory\Model\Stock\Status', 'getProductStockStatus'],
    [
        'getProductStatus',
        'Magento\Catalog\Model\ResourceModel\Product\Status',
        'Magento\CatalogInventory\Model\ResourceModel\Stock\Status',
    ],
    ['updateProductStatus', 'Magento\Catalog\Model\Product\Status'],
    ['getProductStatus', 'Magento\Catalog\Model\Product\Status'],
    [
        'addValueSortToCollection',
        'Magento\Catalog\Model\Product\Status',
        'Magento\CatalogInventory\Model\ResourceModel\Stock\Status',
    ],
    ['catalogProductStatusUpdate', 'Magento\Sales\Model\Observer\Backend\CatalogProductQuote'],
    ['applyPermissionsAfterReindex', 'Magento\CatalogPermissions\Model\Adminhtml\Observer'],
    [
        'transitionProductType',
        'Magento\Catalog\Model\Observer',
        'Magento\Catalog\Model\Product\TypeTransitionManager::processProduct',
    ],
    [
        'transitionProductType',
        'Magento\Downloadable\Model\Observer',
        'Magento\Downloadable\Model\Product\TypeTransitionManager\Plugin\Downloadable::aroundProcessProduct',
    ],
    [
        'isUsedBySuperProducts',
        'Magento\Catalog\Model\ResourceModel\Attribute',
        'Magento\ConfigurableProduct\Model\Attribute\LockValidator::validate',
    ],
    ['_detectMimeType', 'Magento\Framework\File\Transfer\Adapter\Http', '\Magento\Framework\File\Mime::getMimeType()'],
    ['getPublishedFilePath', 'Magento\Framework\View\Publisher'],
    [
        'getPublicFilePath',
        'Magento\Framework\View\PublicFilesManagerInterface',
        'Magento\Framework\View\AssetInterface',
    ],
    ['getPublicFilePath', 'Magento\Framework\View\Publisher', 'Magento\Framework\View\AssetInterface'],
    ['getPublicViewFile', 'Magento\Framework\View\Publisher', 'Magento\Framework\View\AssetInterface'],
    ['_getVatRequiredCustomerAddress', 'Magento\Sales\Model\Observer'],
    ['sendTransactional', 'Magento\Email\Model\Template'],
    ['_getMail', 'Magento\Email\Model\Template'],
    ['send', 'Magento\Email\Model\Template'],
    ['processOrderCreateBefore', 'Magento\GiftCardAccount\Model\Observer'],
    ['googleCheckoutDiscoutItem', 'Magento\GiftCardAccount\Model\Observer'],
    ['googlecheckoutCheckoutBefore', 'Magento\GiftWrapping\Model\Observer'],
    ['injectAnalyticsInGoogleCheckoutLink', 'Magento\GoogleAnalytics\Model\Observer'],
    ['setDefaultLocale', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Resolver'],
    ['getDefaultLocale', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Resolver'],
    ['setLocale', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Resolver'],
    ['getLocale', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Resolver::getLocale'],
    ['setLocaleCode', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Resolver::setLocale'],
    ['emulate', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Resolver'],
    ['revert', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Resolver'],
    ['getTimezone', 'Magento\Core\Model\Locale', 'Magento\Framework\Stdlib\DateTime\Timezone::getDefaultTimezone'],
    ['getDateFormat', 'Magento\Core\Model\Locale', 'Magento\Framework\Stdlib\DateTime\Timezone'],
    ['getDateFormatWithLongYear', 'Magento\Core\Model\Locale', 'Magento\Framework\Stdlib\DateTime\Timezone'],
    ['getTimeFormat', 'Magento\Core\Model\Locale', 'Magento\Framework\Stdlib\DateTime\Timezone'],
    ['getDateTimeFormat', 'Magento\Core\Model\Locale', 'Magento\Framework\Stdlib\DateTime\Timezone'],
    ['date', 'Magento\Core\Model\Locale', 'Magento\Framework\Stdlib\DateTime\Timezone'],
    ['storeDate', 'Magento\Core\Model\Locale', 'Magento\Framework\Stdlib\DateTime\Timezone::scopeDate'],
    [
        'storeDate',
        'Magento\Framework\Stdlib\DateTime\TimezoneInterface',
        'Magento\Framework\Stdlib\DateTime\TimezoneInterface::scopeDate',
    ],
    ['utcDate', 'Magento\Core\Model\Locale'],
    ['storeTimeStamp', 'Magento\Core\Model\Locale', 'Magento\Framework\Stdlib\DateTime\Timezone::scopeTimeStamp'],
    ['formatDate', 'Magento\Core\Model\Locale', 'Magento\Framework\Stdlib\DateTime\Timezone'],
    ['getTranslation', 'Magento\Core\Model\Locale', 'Magento\Framework\Stdlib\DateTime\Timezone::_getTranslation'],
    ['getCurrency', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Currency'],
    ['currency', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Currency::getCurrency'],
    ['getNumber', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Format'],
    ['getJsPriceFormat', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Format::getPriceFormat'],
    ['getOptionLocales', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Lists'],
    ['getTranslatedOptionLocales', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Lists'],
    ['getOptionTimezones', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Lists'],
    ['getOptionWeekdays', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Lists'],
    ['getOptionCountries', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Lists'],
    ['getOptionCurrencies', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Lists'],
    ['getTranslationList', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Lists'],
    ['getCountryTranslation', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Lists'],
    ['getCountryTranslationList', 'Magento\Core\Model\Locale', 'Magento\Framework\Locale\Lists'],
    ['getAllowLocales', 'Magento\Core\Model\Locale'],
    ['getAllowCurrencies', 'Magento\Core\Model\Locale'],
    ['convertMeasureWeight', 'Magento\Usa\Helper\Data', 'Magento\Shipping\Helper\Carrier::convertMeasureWeight'],
    [
        'convertMeasureDimension',
        'Magento\Usa\Helper\Data',
        'Magento\Shipping\Helper\Carrier::convertMeasureDimension',
    ],
    ['getMeasureWeightName', 'Magento\Usa\Helper\Data', 'Magento\Shipping\Helper\Carrier::getMeasureWeightName'],
    [
        'getMeasureDimensionName',
        'Magento\Usa\Helper\Data',
        'Magento\Shipping\Helper\Carrier::getMeasureDimensionName',
    ],
    ['displayGirthValue', 'Magento\Usa\Helper\Data', 'Magento\Usps\Helper\Data::displayGirthValue'],
    ['reindexProductPrices', '\Magento\Catalog\Model\Observer'],
    ['getCustomer', 'Magento\Checkout\Block\Onepage\AbstractOnepage'],
    ['getStoreConfig', 'Magento\Framework\View\Context', '\Magento\Framework\View\Context::getScopeConfig'],
    [
        'getStoreConfig',
        'Magento\Framework\View\Element\Context',
        '\Magento\Framework\View\Element\Context::getScopeConfig',
    ],
    ['setConfig', 'Magento\Store\Model\Store'],
    ['_compareSortOrder', 'Magento\Sales\Model\Config\Ordered'],
    [
        '_toOptionHashOptimized',
        'Magento\Framework\Data\Collection\AbstractDb',
        'Magento\Tax\Model\ResourceModel\Calculation\Rate\Collection::toOptionHashOptimized',
    ],
    ['getSwitchCurrencyUrl', 'Magento\Directory\Block\Currency'],
    ['getPageVarName', 'Magento\Catalog\Block\Product\ProductList\Toolbar'],
    ['getOrderVarName', 'Magento\Catalog\Block\Product\ProductList\Toolbar'],
    ['getDirectionVarName', 'Magento\Catalog\Block\Product\ProductList\Toolbar'],
    ['getModeVarName', 'Magento\Catalog\Block\Product\ProductList\Toolbar'],
    ['getLimitVarName', 'Magento\Catalog\Block\Product\ProductList\Toolbar'],
    ['validatePHPVersion', 'Magento\Framework\Connect\Validator'],
    ['getOrderUrl', 'Magento\Catalog\Block\Product\ProductList\Toolbar'],
    ['getAllOptionsForClass', 'Magento\Tax\Model\Calculation\Rule'],
    ['getModeUrl', 'Magento\Catalog\Block\Product\ProductList\Toolbar'],
    ['getLimitUrl', 'Magento\Catalog\Block\Product\ProductList\Toolbar'],
    ['_getAvailableLimit', 'Magento\Catalog\Block\Product\ProductList\Toolbar'],
    ['getCacheIdTags', 'Magento\Core\Model\AbstractModel'],
    ['addCustomerData', 'Magento\Log\Model\Visitor'],
    ['quoteSubmitAfter', 'Magento\Customer\Model\Observer'],
    ['loadByCustomer', 'Magento\Wishlist\Model\Wishlist'],
    ['_sessionVarCallback', 'Magento\Framework\Url', 'Replaced with inlined closure'],
    ['processReinitConfig', 'Magento\Core\Model\Observer'],
    [
        'reviewsAction',
        'Magento\Catalog\Controller\Adminhtml\Product',
        'Magento\Review\Controller\Adminhtml\Product\Reviews::gridAction',
    ],
    ['initializeTranslation', 'Magento\Backend\Model\Observer'],
    ['_isEmptyTranslateArg', 'Magento\Framework\Translate'],
    ['_getTranslatedString', 'Magento\Framework\Translate'],
    ['initLocale', 'Magento\Framework\Translate'],
    ['_prepareDataString', 'Magento\Framework\Translate'],
    ['getInlineObject', 'Magento\Framework\Translate'],
    ['init', 'Magento\Framework\Translate', 'loadData'],
    ['disable', 'Magento\Framework\Translate\Inline'],
    ['_getAjaxUrl', 'Magento\Framework\Translate\Inline', 'getAjaxUrl'],
    ['_stripInlineTranslations', 'Magento\Framework\Translate\Inline', 'stripInlineTranslations'],
    ['isAllowed', 'Magento\Framework\Translate'],
    ['translate', 'Magento\Framework\Translate'],
    ['_getStoreId', 'Magento\Translation\Model\ResourceModel\Translate', 'getScope'],
    ['_getStoreId', 'Magento\Translation\Model\ResourceModel\String', 'getScope'],
    ['filterByCustomer', 'Magento\Wishlist\Model\ResourceModel\Wishlist\Collection'],
    ['setConfigData', 'Magento\Framework\Module\Setup'],
    ['deleteConfigData', 'Magento\Framework\Module\Setup'],
    ['getReservedAttributes', 'Magento\Catalog\Model\Product'],
    [
        'isReservedAttribute',
        'Magento\Catalog\Model\Product',
        'Magento\Catalog\Model\Product\ReservedAttributeList::isReservedAttribute',
    ],
    [
        'getRatingSummary',
        'Magento\Catalog\Model\Product',
    ],
    ['getCurrentStore', 'Magento\Store\Model\StoreManagerInterface'],
    ['getAnyStoreView', 'Magento\Store\Model\StoreManagerInterface'],
    ['throwStoreException', 'Magento\Store\Model\StoreManagerInterface'],
    ['getCustomer', 'Magento\ProductAlert\Helper\Data'],
    ['addCustomerFilter', 'Magento\ProductAlert\Model\ResourceModel\Stock\Collection'],
    ['addCustomerFilter', 'Magento\ProductAlert\Model\ResourceModel\Price\Collection'],
    ['setCustomer', 'Magento\ProductAlert\Model\Email'],
    ['getCustomer', 'Magento\Persistent\Helper\Session'],
    ['_getThemeInstance', 'Magento\Framework\View\Layout'],
    ['getArea', 'Magento\Framework\View\Layout'],
    ['setArea', 'Magento\Framework\View\Layout'],
    ['getCustomer', 'Magento\Tax\Model\Calculation', 'Magento\Tax\Model\Calculation::getCustomerData'],
    ['setCustomer', 'Magento\Tax\Model\Calculation', 'Magento\Tax\Model\Calculation::setCustomerData'],
    ['setCustomer', 'Magento\Checkout\Model\Session', 'Magento\Checkout\Model\Session::setCustomerData'],
    ['getPersistentName', 'Magento\Persistent\Helper\Data'],
    ['getCustomerDataObject', 'Magento\Persistent\Helper\Session'],
    ['getFlatTableName', 'Magento\Catalog\Model\Indexer\Product\Flat\AbstractAction'],
    ['deleteProductsFromStore', 'Magento\Catalog\Model\Indexer\Product\Flat\AbstractAction'],
    ['getPriceHtml', 'Magento\Rss\Block\Catalog\AbstractCatalog'],
    ['_getPriceBlock', 'Magento\Rss\Block\Catalog\AbstractCatalog'],
    ['_construct', 'Magento\Catalog\Block\Product\Widget\NewWidget'],
    ['_getPriceBlock', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['_getPriceBlockTemplate', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['_preparePriceRenderer', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['addPriceBlock', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getTierPriceHtml', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['_prepareLayout', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getTierPriceHtml', 'Magento\GroupedProduct\Block\Product\View\Type\Grouped'],
    ['_construct', 'Magento\Reports\Block\Product\Widget\Viewed\Item'],
    ['addPriceBlockType', 'Magento\Rss\Block\Wishlist'],
    ['getTierPriceTemplate', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getTierPrices', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getTierPrices', 'Magento\Catalog\Block\Product\Price'],
    ['processTierPrices', 'Magento\Weee\Helper\Data'],
    ['_getCategoryProductIdentities', 'Magento\Catalog\Model\Product'],
    ['_getCategoryIdentities', 'Magento\Catalog\Model\Product'],
    ['_isDataChanged', 'Magento\Catalog\Model\Product'],
    ['getVisibleOnFrontStates', 'Magento\Sales\Model\Order\Config', 'getVisibleOnFrontStatuses'],
    ['getInvisibleOnFrontStates', 'Magento\Sales\Model\Order\Config', 'getInvisibleOnFrontStatuses'],
    ['_authorize', 'Magento\Sales\Model\Order\Payment'],
    ['registerPaymentReviewAction', 'Magento\Sales\Model\Order\Payment'],
    ['_shouldBeConverted', 'Magento\Sales\Model\ResourceModel\AbstractResource'],
    ['_beforeSave', 'Magento\Sales\Model\ResourceModel\AbstractResource'],
    ['_afterSave', 'Magento\Sales\Model\ResourceModel\AbstractResource'],
    ['_afterLoad', 'Magento\Sales\Model\ResourceModel\AbstractResource'],
    ['getAllMethods', 'Magento\Payment\Model\Config'],
    ['_getMethod', 'Magento\Payment\Model\Config'],
    ['getViewFileUrl', 'Magento\Framework\View\Url', 'Magento\Framework\View\Asset\Repository::getUrl'],
    ['getCssFiles', 'Magento\Core\Helper\Theme', 'Magento\Core\Helper\Theme::getCssAssets'],
    ['getGroupedCssFiles', 'Magento\Core\Helper\Theme'],
    ['_detectTheme', 'Magento\Core\Helper\Theme'],
    ['_detectGroup', 'Magento\Core\Helper\Theme'],
    ['_sortThemesByHierarchy', 'Magento\Core\Helper\Theme'],
    ['set', 'Magento\Framework\Api\ExtensibleObjectBuilder'],
    ['toFlatArrayStatic', 'Magento\Framework\Api\ExtensibleDataObjectConverter'],
    ['_sortArrayByArray', 'Magento\Core\Helper\Theme'],
    ['_getGroupLabels', 'Magento\Core\Helper\Theme'],
    ['_sortGroupFilesCallback', 'Magento\Core\Helper\Theme'],
    ['_sortThemesByHierarchyCallback', 'Magento\Core\Helper\Theme'],
    ['_convertFileData', 'Magento\Theme\Block\Adminhtml\System\Design\Theme\Edit\Tab\Css'],
    ['notifyViewFileLocationChanged', 'Magento\Framework\View\FileSystem'],
    [
        'convertModuleNotationToPath',
        'Magento\Framework\View\Asset\PreProcessor\ModuleNotation',
        'Magento\Framework\View\Asset\NotationResolver\Module::convertModuleNotationToPath',
    ],
    ['getViewFile', 'Magento\Framework\View\FileSystem', 'Magento\Framework\View\Asset\File::getSourceFile()'],
    [
        '_unserializeValue',
        'Magento\CatalogInventory\Helper\Minsaleqty',
        'Magento\CatalogInventory\Helper\Minsaleqty::unserializeValue',
    ],
    [
        '_isEncodedArrayFieldValue',
        'Magento\CatalogInventory\Helper\Minsaleqty',
        'Magento\CatalogInventory\Helper\Minsaleqty::isEncodedArrayFieldValue',
    ],
    [
        '_serializeValue',
        'Magento\CatalogInventory\Helper\Minsaleqty',
        'Magento\CatalogInventory\Helper\Minsaleqty::serializeValue',
    ],
    [
        '_fixQty',
        'Magento\CatalogInventory\Helper\Minsaleqty',
        'Magento\CatalogInventory\Helper\Minsaleqty::fixQty',
    ],
    [
        '_encodeArrayFieldValue',
        'Magento\CatalogInventory\Helper\Minsaleqty',
        'Magento\CatalogInventory\Helper\Minsaleqty::encodeArrayFieldValue',
    ],
    [
        '_decodeArrayFieldValue',
        'Magento\CatalogInventory\Helper\Minsaleqty',
        'Magento\CatalogInventory\Helper\Minsaleqty::decodeArrayFieldValue',
    ],
    ['updateOrderAction', 'Magento\Paypal\Controller\Express\AbstractExpress'],
    ['updateOrder', 'Magento\Paypal\Model\Express\Checkout'],
    ['_matchBnCountryCode', 'Magento\Paypal\Model\Config'],
    ['convertOldColumnDefinition', 'Magento\Framework\DB\Helper\AbstractHelper'],
    ['changeItemStatus', 'Magento\CatalogInventory\Model\Stock\Status'],
    ['getWebsiteDefaultStoreId', 'Magento\CatalogInventory\Model\Stock\Status'],
    ['getProductData', 'Magento\CatalogInventory\Model\Stock\Status'],
    ['getProductData', 'Magento\CatalogInventory\Model\ResourceModel\Stock\Status'],
    ['getProduct', 'Magento\CatalogInventory\Model\Stock\Item'],
    ['reset', 'Magento\CatalogInventory\Model\Stock\Item'],
    ['prepareValueForDuplicate', 'Magento\Catalog\Model\Product\Option\Value'],
    ['prepareOptionForDuplicate', '\Magento\Catalog\Model\Product\Option'],
    ['saveOptions', '\Magento\Catalog\Model\Product\Option'],
    [
        'getFlatColums',
        'Magento\Eav\Model\Entity\Attribute\Source\AbstractSource',
        'Magento\Eav\Model\Entity\Attribute\Source\AbstractSource::getFlatColumns',
    ],
    ['addProductAdvanced', '\Magento\Quote\Model\Quote'],
    ['translateArray', 'Magento\Framework\App\Helper\AbstractHelper'],
    ['getCalculator', '\Magento\Tax\Helper\Data'],
    ['getRatesForAllProductTaxClasses', 'Magento\Tax\Model\Calculation'],
    ['getRatesForAllCustomerTaxClasses', 'Magento\Tax\Model\Calculation'],
    ['getMaxRecipients', 'Magento\Catalog\Block\Product\Send'],
    ['prepareCatalogProductIndexSelect', 'Magento\CatalogInventory\Model\ResourceModel\Stock\Status'],
    ['prepareCatalogProductIndexSelect', 'Magento\CatalogInventory\Model\Stock\Status'],
    [
        'getWebsites',
        'Magento\CatalogInventory\Model\StockIndex',
        'Magento\CatalogInventory\Model\StockIndex:getWebsitesWithDefaultStores',
    ],
    ['getProductType', 'Magento\CatalogInventory\Model\StockIndex'],
    ['getProductTypeInstance', 'Magento\CatalogInventory\Model\StockIndex'],
    [
        'getPriceHtml',
        '\Magento\Catalog\Block\Product\AbstractProduct',
        '\Magento\Catalog\Block\Product\AbstractProduct::getProductPriceHtml',
    ],
    ['getDeleteUrl', '\Magento\Checkout\Block\Cart\Item\Renderer'],
    ['sendNewOrderEmail', 'Magento\Sales\Model\Order'],
    ['_getEmails', 'Magento\Sales\Model\Order'],
    ['_getEmails', 'Magento\Sales\Model\Order\Creditmemo'],
    ['sendUpdateEmail', 'Magento\Sales\Model\Order\Creditmemo'],
    ['sendEmail', 'Magento\Sales\Model\Order\Creditmemo'],
    ['sendEmail', 'Magento\Sales\Model\Order\Invoice'],
    ['sendUpdateEmail', 'Magento\Sales\Model\Order\Invoice'],
    ['_getEmails', 'Magento\Sales\Model\Order\Invoice'],
    ['sendEmail', 'Magento\Sales\Model\Order\Shipment'],
    ['sendUpdateEmail', 'Magento\Sales\Model\Order\Shipment'],
    ['_getEmails', 'Magento\Sales\Model\Order\Shipment'],
    ['rebuildIndex', 'Magento\CatalogSearch\Model\Fulltext', 'Magento\CatalogSearch\Model\Indexer\Fulltext::execute'],
    ['cleanIndex', 'Magento\CatalogSearch\Model\Fulltext'],
    ['setAllowTableChanges', 'Magento\CatalogSearch\Model\Fulltext'],
    ['updateCategoryIndex', 'Magento\CatalogSearch\Model\Fulltext'],
    [
        'rebuildIndex',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::rebuildIndex',
    ],
    [
        '_rebuildStoreIndex',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::rebuildStoreIndex',
    ],
    [
        '_getSearchableProducts',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::getSearchableProducts',
    ],
    [
        'cleanIndex',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::cleanIndex',
    ],
    [
        'getEavConfig',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::getEavConfig',
    ],
    [
        '_getSearchableAttributes',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::getSearchableAttributes',
    ],
    [
        '_getSearchableAttribute',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::getSearchableAttribute',
    ],
    [
        '_unifyField',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::unifyField',
    ],
    [
        '_getProductAttributes',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::getProductAttributes',
    ],
    [
        '_getProductTypeInstance',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::getProductTypeInstance',
    ],
    [
        '_getProductChildIds',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::getProductChildIds',
    ],
    [
        '_getProductEmulator',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::getProductEmulator',
    ],
    [
        '_prepareProductIndex',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::prepareProductIndex',
    ],
    [
        '_getAttributeValue',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::getAttributeValue',
    ],
    [
        '_saveProductIndex',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
    ],
    [
        '_saveProductIndexes',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::saveProductIndexes',
    ],
    [
        '_getStoreDate',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
        'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full::getStoreDate',
    ],
    [
        'updateCategoryIndex',
        'Magento\CatalogSearch\Model\ResourceModel\Fulltext',
    ],
    ['_getRestrictedStoresList', 'Magento\UrlRewrite\Block\Edit\Form'],
    ['removeTag', 'Magento\UrlRewrite\Model\UrlRewrite'],
    ['addTag', 'Magento\UrlRewrite\Model\UrlRewrite'],
    ['loadByTags', 'Magento\UrlRewrite\Model\UrlRewrite'],
    ['canApplyMsrp', 'Magento\Checkout\Block\Cart\AbstractCart', 'Magento\Msrp\Block\Total'],
    ['createController', 'Magento\Framework\App\ActionFactory', 'Magento\Framework\App\ActionFactory::create'],
    ['getMethodFormBlock', 'Magento\Centinel\Helper\Data'],
    ['login', 'Magento\Customer\Model\Session'],
    ['roundPrice', 'Magento\Store\Model\Store', 'Magento\Framework\Pricing\PriceCurrencyInterface::round'],
    ['formatPrice', 'Magento\Store\Model\Store', 'Magento\Framework\Pricing\PriceCurrencyInterface::format'],
    ['convertPrice', 'Magento\Store\Model\Store', 'Magento\Framework\Pricing\PriceCurrencyInterface::convert'],
    ['getPriceFilter', 'Magento\Store\Model\Store'],
    ['setPricesModel', 'Magento\Catalog\Model\Layer\Filter\Price\Algorithm'],
    ['loadPrices', 'Magento\Catalog\Model\Layer\Filter\Price', 'Magento\CatalogSearch\Model\Price\Interval::load'],
    [
        'loadPreviousPrices',
        'Magento\Catalog\Model\Layer\Filter\Price',
        'Magento\CatalogSearch\Model\Price\Interval::loadPrevious',
    ],
    [
        'loadNextPrices',
        'Magento\Catalog\Model\Layer\Filter\Price',
        'Magento\CatalogSearch\Model\Price\Interval::loadNext',
    ],
    ['getQuery', 'Magento\CatalogSearch\Helper\Data', 'Magento\Search\Model\QueryFactory::get'],
    ['getQueryText', 'Magento\CatalogSearch\Helper\Data', 'Magento\Search\Model\Query::getQueryText'],
    ['_addHeader', 'Magento\Rss\Model\Rss'],
    ['_addEntries', 'Magento\Rss\Model\Rss'],
    ['_addEntry', 'Magento\Rss\Model\Rss'],
    ['_welcomeCustomer', 'Magento\Customer\Controller\Account'],
    ['_addWelcomeMessage', 'Magento\Customer\Controller\Account'],
    ['_isVatValidationEnabled', 'Magento\Customer\Controller\Account'],
    ['_createUrl', 'Magento\Customer\Controller\Account'],
    ['_extractAddress', 'Magento\Customer\Controller\Account\CreatePost', 'Magento\Customer\Controller\Account\CreatePost::extractAddress'],
    ['_loginPostRedirect', 'Magento\Customer\Controller\Account\LoginPost', 'Magento\Customer\Model\Account\Redirect::getRedirect'],
    ['_getAllowedActions', 'Magento\Customer\Controller\Account', 'Magento\Customer\Controller\Account::getAllowedActions'],
    ['isRegistrationAllowed', 'Magento\Customer\Controller\Account\CreatePost'],
    ['isRegistrationAllowed', 'Magento\Invitation\Controller\Customer\Account\CreatePost'],
    ['getNoDisplay', 'Magento\Catalog\Block\Adminhtml\Product\Helper\Form\Category'],
    ['getSearchType', 'Magento\CatalogSearch\Model\Fulltext'],
    ['getEmailConfirmationUrl', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Url::getEmailConfirmationUrl'],
    ['getForgotPasswordUrl', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Url::getForgotPasswordUrl'],
    ['getEditPostUrl', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Url::getEditPostUrl'],
    ['getEditUrl', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Url::getEditUrl'],
    ['getRegisterPostUrl', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Url::getRegisterPostUrl'],
    ['getRegisterUrl', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Url::getRegisterUrl'],
    ['getAccountUrl', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Url::getAccountUrl'],
    ['getDashboardUrl', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Url::getDashboardUrl'],
    ['getLogoutUrl', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Url::getLogoutUrl'],
    ['getLoginPostUrl', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Url::getLoginPostUrl'],
    ['getLoginUrl', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Url::getLoginUrl'],
    ['getLoginUrlParams', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Url::getLoginUrlParams'],
    ['getMerchantCountryCode', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Vat::getMerchantCountryCode'],
    ['getMerchantVatNumber', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Vat::getMerchantVatNumber'],
    ['getCustomerGroupIdBasedOnVatNumber', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Vat::getCustomerGroupIdBasedOnVatNumber'],
    ['checkVatNumber', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Vat::checkVatNumber'],
    ['createVatNumberValidationSoapClient', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Vat::createVatNumberValidationSoapClient'],
    ['canCheckVatNumber', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Vat::canCheckVatNumber'],
    ['getCustomerVatClass', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Vat::getCustomerVatClass'],
    ['isCountryInEU', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Vat::isCountryInEU'],
    ['generateResetPasswordLinkToken', 'Magento\Customer\Helper\Data'],
    ['isLoggedIn', 'Magento\Customer\Helper\Data'],
    ['getResetPasswordLinkExpirationPeriod', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Customer::getResetPasswordLinkExpirationPeriod'],
    ['isRegistrationAllowed', 'Magento\Customer\Helper\Data', 'Magento\Customer\Model\Registration::isAllowed'],
    ['getFlatIndexer', 'Magento\Catalog\Model\Category'],
    ['getProductIndexer', 'Magento\Catalog\Model\Category'],
    ['getFlatIndexer', 'Magento\Catalog\Model\Indexer\AbstractFlatState'],
    ['getIndexer', 'Magento\Catalog\Model\Indexer\Category\Product\Plugin\StoreGroup'],
    ['getIndexer', 'Magento\Catalog\Model\Indexer\Category\Flat\Plugin\StoreGroup'],
    ['getIndexer', 'Magento\Catalog\Model\Indexer\Product\Price\Plugin\AbstractPlugin'],
    ['getCategoryIndexer', 'Magento\Catalog\Model\Product'],
    ['getCategoryIndexer', 'Magento\Catalog\Model\Product\Action'],
    ['getIndexer', 'Magento\CatalogSearch\Model\Indexer\Fulltext\Plugin\AbstractPlugin'],
    ['getRemoteResource', 'Magento\Framework\Filesystem', 'Magento\Framework\Filesystem\File\ReadFactory::create'],
    ['setCache', 'Magento\Framework\App\Magento\Framework\App\ResourceConnection'],
    ['setTablePrefix', 'Magento\Framework\App\Magento\Framework\App\ResourceConnection', 'Can be passed through constructor only'],
    ['_debugTimer', 'Magento\Framework\DB\Adapter\Pdo\Mysql', 'Magento\Framework\DB\Logger\LoggerAbstract::startTimer'],
    ['_debugStat', 'Magento\Framework\DB\Adapter\Pdo\Mysql', 'Magento\Framework\DB\Logger\File::logStats'],
    ['_debugException', 'Magento\Framework\DB\Adapter\Pdo\Mysql', 'Magento\Framework\DB\Logger\File::critical'],
    ['_debugWriteToFile', 'Magento\Framework\DB\Adapter\Pdo\Mysql', 'Magento\Framework\DB\Logger\File::log'],
    ['applyDataUpdates', 'Magento\Framework\Module\Setup', '\Magento\Framework\Setup\ModuleDataSetupInterface::applyDataUpdates'],
    ['_installData', 'Magento\Framework\Module\Setup', 'Magento\Setup\Module\DataSetup::_installData'],
    ['_upgradeData', 'Magento\Framework\Module\Setup', 'Magento\Setup\Module\DataSetup::_upgradeData'],
    ['_getAvailableDataFiles', 'Magento\Framework\Module\Setup', 'Magento\Setup\Module\DataSetup::_getAvailableDataFiles'],
    ['_modifyResourceDb', 'Magento\Framework\Module\Setup', 'Magento\Setup\Module\DataSetup::_modifyResourceDb'],
    ['_includeFile', 'Magento\Framework\Module\Setup', 'Magento\Setup\Module\DataSetup::_includeFile'],
    ['_getModifySqlFiles', 'Magento\Framework\Module\Setup', 'Magento\Setup\Module\DataSetup::_getModifySqlFiles'],
    ['getTableRow', 'Magento\Framework\Module\Setup', '\Magento\Framework\Setup\ModuleDataSetupInterface::getTableRow'],
    ['deleteTableRow', 'Magento\Framework\Module\Setup', '\Magento\Framework\Setup\ModuleDataSetupInterface::deleteTableRow'],
    ['updateTableRow', 'Magento\Framework\Module\Setup', '\Magento\Framework\Setup\ModuleDataSetupInterface::updateTableRow'],
    ['getCallAfterApplyAllUpdates', 'Magento\Framework\Module\Setup', '\Magento\Framework\Setup\ModuleDataSetupInterface::getCallAfterApplyAllUpdates'],
    ['afterApplyAllUpdates', 'Magento\Framework\Module\Setup', '\\Magento\Framework\Setup\ModuleDataSetupInterface::afterApplyAllUpdates'],
    ['getEventManager', 'Magento\Framework\Module\Setup', '\Magento\Framework\Setup\ModuleDataSetupInterface::getEventManager'],
    ['getFilesystem', 'Magento\Framework\Module\Setup', '\Magento\Framework\Setup\ModuleDataSetupInterface::getFilesystem'],
    ['createMigrationSetup', 'Magento\Framework\Module\Setup', '\Magento\Framework\Setup\ModuleDataSetupInterface::createMigrationSetup'],
    ['getModules', 'Magento\Framework\Module\ModuleList', 'getAll'],
    ['getModules', 'Magento\Framework\Module\ModuleListInterface', 'getAll'],
    ['getModule', 'Magento\Framework\Module\ModuleList', 'getOne'],
    ['getModule', 'Magento\Framework\Module\ModuleListInterface', 'getOne'],
    ['_getNewConditionModelInstance', 'Magento\Rule\Model\Condition\Combine'],
    ['getItemsRandomCollection', 'Magento\Sales\Model\Order'],
    ['importOrderAddress', 'Magento\Quote\Model\Quote\Address', 'Magento\Quote\Model\Quote\Address::importCustomerAddressData'],
    ['_getUsedAttributes', 'Magento\SalesRule\Model\Rule'],
    ['getDefaultCustomerGroupId', 'Magento\Customer\Model\Vat', 'Magento\Customer\Api\GroupManagementInterface::getDefaultGroup'],
    [
        'getRatesByCustomerAndProductTaxClassId',
        'Magento\Tax\Service\V1\TaxRuleServiceInterface',
        'Magento\Tax\Api\TaxRateManagementInterface::getRatesByCustomerAndProductTaxClassId',
    ],
    ['getProduct', 'Magento\Catalog\Helper\Product'],
    ['getCategoryModel', 'Magento\Catalog\Model\ResourceModel\Url'],
    ['getTaxConfig', 'Magento\ConfigurableProduct\Pricing\Price\AttributePrice'],
    ['prepareAdjustmentConfig', 'Magento\ConfigurableProduct\Pricing\Price\AttributePrice'],
    ['_escapeDefaultValue', 'Magento\Framework\Code\Generator\EntityAbstract'],
    ['urlEncode', 'Magento\Framework\App\Helper\AbstractHelper', 'Magento\Framework\Url\EncoderInterface::encode'],
    ['urlDecode', 'Magento\Framework\App\Helper\AbstractHelper', 'Magento\Framework\Url\DecoderInterface::decode'],
    ['isModuleEnabled', 'Magento\Framework\App\Helper\AbstractHelper', 'Magento\Framework\Module\Manager::isEnabled()'],
    ['isModuleOutputEnabled', 'Magento\Framework\App\Helper\AbstractHelper', 'Magento\Framework\Module\Manager::isOutputEnabled()'],
    ['_packToTar', 'Magento\Framework\Archive\Tar'],
    ['_parseHeader', 'Magento\Framework\Archive\Tar'],
    ['getIdentities', 'Magento\Wishlist\Block\Link'],
    ['_getCustomerSession', 'Magento\Wishlist\Block\AbstractBlock'],
    ['formatPriceString', 'Magento\Bundle\Block\Catalog\Product\View\Type\Bundle\Option'],
    ['getTierPriceCount', 'Magento\Catalog\Model\Product'],
    ['getFormatedTierPrice', 'Magento\Catalog\Model\Product'],
    ['_getWebsiteRates', 'Magento\Catalog\Model\Product\Attribute\Backend\Tierprice'],
    ['toString', 'Magento\CatalogRule\Model\Rule'],
    ['toArray', 'Magento\CatalogRule\Model\Rule'],
    ['addStoreFilter', 'Magento\Cms\Model\ResourceModel\AbstractCollection'],
    ['asString', 'Magento\Rule\Model\AbstractModel'],
    ['asHtml', 'Magento\Rule\Model\AbstractModel'],
    ['asArray', 'Magento\Rule\Model\AbstractModel'],
    ['_prepareWebsiteIds', 'Magento\Rule\Model\AbstractModel'],
    ['setEnv', 'Magento\Rule\Model\ResourceModel\Rule\Collection\AbstractCollection'],
    ['getEnv', 'Magento\Rule\Model\ResourceModel\Rule\Collection\AbstractCollection'],
    ['setActiveFilter', 'Magento\Rule\Model\ResourceModel\Rule\Collection\AbstractCollection'],
    ['process', 'Magento\Rule\Model\ResourceModel\Rule\Collection\AbstractCollection'],
    [
        'isAutomaticCleaningAvailable',
        'Magento\Framework\Cache\Backend\Decorator\AbstractDecorator',
        'Magento\Framework\Cache\Backend\Decorator\AbstractDecorator::getCapabilities',
    ],
    [
        'getNoteNotify',
        'Magento\Sales\Block\Adminhtml\Order\Create\Comment',
        'Magento\Sales\Block\Adminhtml\Order\Create\Totals',
    ],
    ['getLinksConfig', 'Magento\Downloadable\Block\Catalog\Product\Links'],
    ['getAuthorizationAmounts', 'Magento\Paypal\Model\Config'],
    ['getSkinImagePlaceholderUrl', 'Magento\Cms\Model\Wysiwyg\Config'],
    ['compareIndexColumnProperties', 'Magento\Catalog\Model\ResourceModel\Helper'],
    ['getIsNullNotNullCondition', 'Magento\Catalog\Model\ResourceModel\Helper'],
    ['getAllNonNominalItems', 'Magento\Sales\Model\Quote\Address'],
    ['getAllNominalItems', 'Magento\Sales\Model\Quote\Address'],
    ['isNominal', 'Magento\Sales\Model\Order\Item'],
    ['getIsConfigurable', 'Magento\Catalog\Api\Data\EavAttributeInterface'],
    ['getIsConfigurable', 'Magento\Catalog\Model\Category\Attribute'],
    ['getIsConfigurable', 'Magento\Catalog\Model\ResourceModel\Eav\Attribute'],
    ['getIsNominal', 'Magento\Sales\Model\Quote\Item\AbbstractItem'],
    ['checkQuoteAmount', 'Magento\Sales\Helper\Data'],
    ['getEntityTypeId', 'Magento\Customer\Model\Customer'],
    ['getResource', 'Magento\CatalogSearch\Model\ResourceModel\EngineInterface'],
    ['getResourceCollection', 'Magento\CatalogSearch\Model\ResourceModel\EngineInterface'],
    ['getResultCollection', 'Magento\CatalogSearch\Model\ResourceModel\EngineInterface'],
    ['getAdvancedResultCollection', 'Magento\CatalogSearch\Model\ResourceModel\EngineInterface'],
    ['_getSendfriendModel', 'Magento\Sendfriend\Block\Send'],
    ['_initSendToFriendModel', 'Magento\Sendfriend\Controller\Product'],
    ['register', 'Magento\Sendfriend\Model\Sendfriend'],
    ['_getImageHelper', 'Magento\Catalog\Model\Product'],
    ['streamOpen', 'Magento\Framework\Io\File'],
    ['getPreProcessors', 'Magento\Framework\View\Asset\PreProcessor\Pool', 'process'],
    ['isPhpFile', '\Magento\Tools\Di\Code\Reader\ClassesScanner'],
    ['renderPage', 'Magento\Cms\Helper\Page'],
    ['renderPageExtended', 'Magento\Cms\Helper\Page'],
    ['_renderPage', 'Magento\Cms\Helper\Page'],
    ['beforeGenerateBlock', 'Magento\Backend\Model\View\Layout\Builder'],
    ['beforeGenerateBlock', 'Magento\Backend\Model\View\Page\Builder'],
    ['filterAclNodes', 'Magento\Backend\Model\View\Layout\Filter\Acl', 'Magento\Backend\Model\View\Layout\Filter\Acl::filterAclElements'],
    ['getModuleName', 'Magento\Framework\App\Console\Request'],
    ['setModuleName', 'Magento\Framework\App\Console\Request'],
    ['getActionName', 'Magento\Framework\App\Console\Request'],
    ['setActionName', 'Magento\Framework\App\Console\Request'],
    ['getCookie', 'Magento\Framework\App\Console\Request'],
    ['setRoutingInfo', 'Magento\Framework\App\Request\Http'],
    ['rewritePathInfo', 'Magento\Framework\App\Request\Http'],
    ['getRequestedActionName', 'Magento\Framework\App\Request\Http'],
    ['getRequestedControllerName', 'Magento\Framework\App\Request\Http'],
    ['getRequestedRouteName', 'Magento\Framework\App\Request\Http'],
    ['isStraight', 'Magento\Framework\App\Request\Http'],
    ['getBaseUrl', 'Magento\Framework\App\Request\Http', 'Magento\Framework\HTTP\PhpEnvironment\Request'],
    ['getAliases', 'Magento\Framework\App\Request\Http'],
    ['setPathInfo', 'Magento\Framework\App\Request\Http', 'Magento\Framework\HTTP\PhpEnvironment\Request'],
    ['getRawBody', 'Magento\Framework\View\Context', 'getContent'],
    ['setPost', 'Magento\Framework\App\Request\Http', 'Magento\Framework\HTTP\PhpEnvironment\Request'],
    ['getRequestString', 'Magento\Framework\App\Request\Http', 'Magento\Framework\HTTP\PhpEnvironment\Request'],
    ['getFiles', 'Magento\Framework\App\Request\Http'],
    ['getAlias', 'Magento\Framework\App\Request\Http', 'Magento\Framework\HTTP\PhpEnvironment\Request'],
    ['setAlias', 'Magento\Framework\App\Request\Http', 'Magento\Framework\HTTP\PhpEnvironment\Request'],
    [
        'render',
        'Magento\Framework\Webapi\ErrorProcessor',
        'Magento\Framework\Webapi\ErrorProcessor::renderErrorMessage',
    ],
    [
        'processServiceOutput',
        'Magento\Webapi\Controller\Rest\Response\DataObjectConverter',
        'Magento\Framework\Webapi\ServiceOutputProcessor::process',
    ],
    [
        'getInputData',
        'Magento\Webapi\Controller\ServiceArgsSerializer',
        'Magento\Framework\Webapi\ServiceInputProcessor::process',
    ],
    [
        'getServiceName',
        'Magento\Webapi\Helper\Data',
        'Magento\Webapi\Model\Soap\Config::getServiceName',
    ],
    [
        'getServiceNameParts',
        'Magento\Webapi\Helper\Data',
        'Magento\Webapi\Model\Soap\Config::getServiceNameParts',
    ],
    [
        'getSelectedResources',
        'Magento\Webapi\Helper\Data',
        'Magento\Webapi\Model\Soap\Config::getSelectedResources',
    ],
    [
        'validateCredentials',
        'Magento\Integration\Helper\Validator',
        'Magento\Integration\Model\CredentialsValidator::validate',
    ],
    ['getGlobalIcon', '\Magento\Backend\Block\Widget'],
    ['getSuccessRedirect', 'Magento\Customer\Controller\Account\CreatePost'],
    [
        'loginPostRedirect',
        'Magento\Customer\Controller\Account\LoginPost',
        'Magento\Customer\Model\Account\Redirect::getRedirect',
    ],
    [
        'process',
        'Magento\Framework\Api\ExtensionAttributesFactory',
        'Magento\Framework\Api\ExtensionAttribute\JoinProcessor::process'
    ],
    [
        'extractExtensionAttributes',
        'Magento\Framework\Api\ExtensionAttributesFactory',
        'Magento\Framework\Api\ExtensionAttribute\JoinProcessor::extractExtensionAttributes'
    ],
    ['isReviewOwner', 'Magento\Review\Block\Customer\View'],
    ['getRegistration', 'Magento\Customer\Block\Form\Login', 'Magento\Customer\Block\Form\Login\Info::getRegistration'],
    ['getCreateAccountUrl', 'Magento\Customer\Block\Form\Login', 'Magento\Customer\Block\Form\Login\Info::getCreateAccountUrl'],
    ['getSuccessMessage', 'Magento\Newsletter\Block\Subscribe'],
    ['getErrorMessage', 'Magento\Newsletter\Block\Subscribe'],
    ['_initCollection', 'Magento\Review\Block\Customer\ListCustomer'],
    ['count', 'Magento\Review\Block\Customer\ListCustomer'],
    ['_getCollection', 'Magento\Review\Block\Customer\ListCustomer'],
    ['getCollection', 'Magento\Review\Block\Customer\ListCustomer', 'Magento\Review\Block\Customer\ListCustomer::getReviews'],
    ['_initCollection', 'Magento\Review\Block\Customer\Recent'],
    ['count', 'Magento\Review\Block\Customer\Recent'],
    ['_getCollection', 'Magento\Review\Block\Customer\Recent'],
    ['getCollection', 'Magento\Review\Block\Customer\Recent', 'Magento\Review\Block\Customer\Recent::getReviews'],
    ['getProductEntityTypeId', 'Magento\Reports\Model\ResourceModel\Product\Collection'],
    ['setProductEntityTypeId', 'Magento\Reports\Model\ResourceModel\Product\Collection'],
    ['bindLocale', 'Magento\Backend\Model\Observer'],
    ['getLocaleLists', 'Magento\Dhl\Model\Resource\Setup', 'getLocaleResolver'],
    ['getTranslationList', 'Magento\Framework\Locale\Lists', '\ResourceBundle'],
    ['getCountryTranslationList', 'Magento\Framework\Locale\Lists', '\ResourceBundle'],
    ['getTranslationList', 'Magento\Framework\Locale\ListsInterface', '\ResourceBundle'],
    ['getCountryTranslationList', 'Magento\Framework\Locale\ListsInterface', '\ResourceBundle'],
    ['setLocaleCode', 'Magento\Framework\Locale\ResolverInterface', 'setLocale'],
    ['getLocaleCode', 'Magento\Framework\Locale\ResolverInterface', 'getLocale'],
    ['setLocaleCode', 'Magento\Framework\Locale\Resolver', 'setLocale'],
    ['getLocaleCode', 'Magento\Framework\Locale\Resolver', 'getLocale'],
    ['_getTranslation', 'Magento\Framework\Stdlib\DateTime\Timezone', '\ResourceBundle'],
    ['formatTime', 'Magento\Framework\Stdlib\DateTime\TimezoneInterface', 'formatDateTime'],
    ['utcDate', 'Magento\Framework\Stdlib\DateTime\TimezoneInterface'],
    ['formatTime', 'Magento\Framework\Stdlib\DateTime\Timezone', 'formatDateTime'],
    ['utcDate', 'Magento\Framework\Stdlib\DateTime\Timezone'],
    ['getLocaleCode', 'Magento\Framework\View\Asset\File\FallbackContext', 'getLocale'],
    ['getLocaleCode', 'Magento\Paypal\Model\Api\AbstractApi', 'getLocale'],
    ['getCollectionAttribute', 'Magento\Eav\Model\Config'],
    ['loadCollectionAttributes', 'Magento\Eav\Model\Config'],
    ['_isCacheEnabled', 'Magento\Eav\Model\Config'],
    ['_createCustomerAttribute', '\Magento\Customer\Model\Customer'],
    ['prepareCatalogProductPriceIndexTable', 'Magento\CatalogRule\Model\Observer'],
    ['joinOrders', 'Magento\Reports\Model\ResourceModel\Customer\Collection'],
    ['addOrdersCount', 'Magento\Reports\Model\ResourceModel\Customer\Collection'],
    ['addSumAvgTotals', 'Magento\Reports\Model\ResourceModel\Customer\Collection'],
    ['orderByTotalAmount', 'Magento\Reports\Model\ResourceModel\Customer\Collection'],
    ['addOrderedQty', 'Magento\Reports\Model\ResourceModel\Product\Collection'],
    ['prepareForProductsInCarts', 'Magento\Reports\Model\ResourceModel\Quote\Collection'],
    ['getOrdersSubSelect', 'Magento\Reports\Model\ResourceModel\Quote\Collection'],
    ['isStateProtected', 'Magento\Sales\Model\Order'],
    ['_getBundleOptions', 'Magento\Bundle\Block\Checkout\Cart\Item\Renderer'],
    ['_getSelectionFinalPrice', 'Magento\Bundle\Block\Checkout\Cart\Item\Renderer'],
    ['_getSelectionQty', 'Magento\Bundle\Block\Checkout\Cart\Item\Renderer'],
    ['getItemCount', 'Magento\Checkout\Block\Cart\Sidebar', 'Moved to Magento\Checkout\CustomerData\Cart'],
    ['getRecentItems', 'Magento\Checkout\Block\Cart\Sidebar', 'Moved to Magento\Checkout\CustomerData\Cart'],
    ['getSubtotal', 'Magento\Checkout\Block\Cart\Sidebar', 'Moved to Magento\Checkout\CustomerData\Cart'],
    ['getSummaryCount', 'Magento\Checkout\Block\Cart\Sidebar', 'Moved to Magento\Checkout\CustomerData\Cart'],
    ['isPossibleOnepageCheckout', 'Magento\Checkout\Block\Cart\Sidebar', 'Moved to Magento\Checkout\CustomerData\Cart'],
    ['getItems', 'Magento\Checkout\Block\Cart\Sidebar', 'Moved to Magento\Checkout\CustomerData\Cart'],
    ['getSummaryText', 'Magento\Checkout\Block\Cart\Sidebar', 'Moved to Magento\Checkout\CustomerData\Cart'],
    ['getCacheKeyInfo', 'Magento\Checkout\Block\Cart\Sidebar'],
    ['_serializeRenders', 'Magento\Checkout\Block\Cart\Sidebar'],
    ['deserializeRenders', 'Magento\Checkout\Block\Cart\Sidebar'],
    ['getIdentities', 'Magento\Checkout\Block\Cart\Sidebar'],
    [
        'validateMinimunAmount',
        'Magento\Checkout\Block\Cart\ValidationMessages',
        'Magento\Checkout\Block\Cart\ValidationMessages::validateMinimunAmount',
    ],
    ['getCustomerName', 'Magento\Customer\Block\Account\Customer', 'Moved to Magento\Customer\CustomerData'],
    ['_prepareCollection', 'Magento\Wishlist\Block\Customer\Sidebar'],
    ['_toHtml', 'Magento\Wishlist\Block\Customer\Sidebar'],
    [
        'getCanDisplayWishlist',
        'Magento\Wishlist\Block\Customer\Sidebar',
        'Moved to Magento\Wishlist\CustomerData\Wishlist',
    ],
    ['getWishlistItems', 'Magento\Wishlist\Block\Customer\Sidebar', 'Moved to Magento\Wishlist\CustomerData\Wishlist'],
    ['getItemCount', 'Magento\Wishlist\Block\Customer\Sidebar', 'Moved to Magento\Wishlist\CustomerData\Wishlist'],
    ['hasWishlistItems', 'Magento\Wishlist\Block\Customer\Sidebar', 'Moved to Magento\Wishlist\CustomerData\Wishlist'],
    ['getIdentities', 'Magento\Wishlist\Block\Customer\Sidebar'],
    [
        'getAddAllToCartUrl',
        'Magento\Wishlist\Block\Customer\Sidebar',
        'Magento\Wishlist\Block\Customer\Sidebar::getAddAllToCartParams',
    ],
    [
        'getItemAddToCartUrl',
        'Magento\Wishlist\Block\AbstractBlock',
        'Magento\Wishlist\Block\AbstractBlock::getItemAddToCartParams',
    ],
    ['getCounter', 'Magento\Wishlist\Block', 'Moved to Magento\Wishlist\CustomerData\Wishlist'],
    ['_getItemCount', 'Magento\Wishlist\Block', 'Moved to Magento\Wishlist\CustomerData\Wishlist'],
    ['_createCounter', 'Magento\Wishlist\Block', 'Moved to Magento\Wishlist\CustomerData\Wishlist'],
    ['getTitle', 'Magento\Wishlist\Block'],
    [
        'initOrders',
        'Magento\Sales\Block\Reorder\Sidebar',
        'Moved to \Magento\Sales\CustomerData\LastOrderedItems',
    ],
    ['getItems', 'Magento\Sales\Block\Reorder\Sidebar', 'Moved to \Magento\Sales\CustomerData\LastOrderedItems'],
    [
        'isItemAvailableForReorder',
        'Magento\Sales\Block\Reorder\Sidebar',
        'Moved to \Magento\Sales\CustomerData\LastOrderedItems',
    ],
    [
        'getLastOrder',
        'Magento\Sales\Block\Reorder\Sidebar',
        'Moved to \Magento\Sales\CustomerData\LastOrderedItems',
    ],
    ['getIdentities', 'Magento\Sales\Block\Reorder\Sidebar'],
    ['assignData', 'Magento\OfflinePayments\Model\Checkmo'],
    ['timeShift', 'Magento\Reports\Model\ResourceModel\Report\Collection'],
    ['getFilePath', 'Magento\MediaStorage\Model\File\Storage\Request'],
    ['_getStoreTimezoneUtcOffset', 'Magento\Reports\Model\ResourceModel\Report\AbstractReport'],
    ['_dateToUtc', 'Magento\Reports\Model\ResourceModel\Report\AbstractReport'],
    ['getDataSetDefault', 'Magento\Framework\DataObject'],
    ['isDeleted', 'Magento\Framework\DataObject', 'Moved to Magento\Framework\Model\AbstractModel'],
    ['hasDataChanges', 'Magento\Framework\DataObject', 'Moved to Magento\Framework\Model\AbstractModel'],
    ['setIdFieldName', 'Magento\Framework\DataObject', 'Moved to Magento\Framework\Model\AbstractModel'],
    ['getIdFieldName', 'Magento\Framework\DataObject', 'Moved to Magento\Framework\Model\AbstractModel'],
    ['setOrigData', 'Magento\Framework\DataObject', 'Moved to Magento\Framework\Model\AbstractModel'],
    ['getOrigData', 'Magento\Framework\DataObject', 'Moved to Magento\Framework\Model\AbstractModel'],
    ['dataHasChangedFor', 'Magento\Framework\DataObject', 'Moved to Magento\Framework\Model\AbstractModel'],
    ['setDataChanges', 'Magento\Framework\DataObject', 'Moved to Magento\Framework\Model\AbstractModel'],
    [
        'getConfigureUrl',
        'Magento\Checkout\Block\Cart\Item\Renderer',
        'Magento\Checkout\Block\Cart\Item\Renderer\Actions\Edit::getConfigureUrl',
    ],
    ['getCurrenCategoryKey', 'Magento\Catalog\Block\Navigation', 'getCurrentCategoryKey'],
    ['getUsedDefaultForPaths', 'Magento\Email\Block\Adminhtml\Template\Edit'],
    ['getSystemConfigPathsWhereUsedAsDefault', 'Magento\Email\Model\BackendTemplate'],
    ['_findEmailTemplateUsages', 'Magento\Email\Model\BackendTemplate'],
    [
        'getSystemConfigPathsWhereUsedCurrently',
        'Magento\Email\Model\BackendTemplate',
        'Magento\Email\Model\BackendTemplate::getSystemConfigPathsWhereCurrentlyUsed',
    ],
    [
        'getUsedCurrentlyForPaths',
        'Magento\Email\Block\Adminhtml\Template\Edit',
        'Magento\Email\Block\Adminhtml\Template\Edit::getCurrentlyUsedForPaths',
    ],
    ['_implodeStreetValue', 'Magento\Customer\Model\Address\AbstractAddress', '_implodeArrayValues',],
    ['_implodeStreetField', 'Magento\Customer\Model\Address\AbstractAddress', '_implodeArrayField',],
    ['_applyDesignConfig', 'Magento\Email\Model\AbstractTemplate', 'applyDesignConfig'],
    ['_cancelDesignConfig', 'Magento\Email\Model\AbstractTemplate', 'cancelDesignConfig'],
    ['loadByCode', 'Magento\Newsletter\Model\ResourceModel'],
    ['_getIncludeParameters', 'Magento\Framework\Filter\Template', '_getParameters'],
    ['setIncludeProcessor', 'Magento\Framework\Filter\Template'],
    ['includeDirective', 'Magento\Framework\Filter\Template'],
    ['getCentinelValidator', 'Magento\Paypal\Model\Direct'],
    ['_getCentinelVpasLabel', 'Magento\Paypal\Model\Info'],
    ['_getCentinelEciLabel', 'Magento\Paypal\Model\Info'],
    ['_getPayPalPayflowPro3dSecure', 'Magento\Config\Test\Repository\Config'],
    ['_getPayPalPaymentsPro3dSecure', 'Magento\Config\Test\Repository\Config'],
    [
        'getCreatedAtFormated',
        'Magento\Sales\Model\Order',
        'Magento\Sales\Model\Order::getCreatedAtFormatted',
    ],
    [
        '_getConfig',
        'Magento\Store\Model\Store',
        'Magento\Store\Model\Store::getConfig',
    ],
    ['addAdditionalFieldsToResponseFrontend', 'Magento\Authorizenet\Model\Directpost\Observer'],
    ['_getAuthorizeNet3dSecure', 'Magento\Config\Test\Repository\Config'],
    [
        'getRelyUrl',
        'Magento\Authorizenet\Helper\Backend\Data',
        'Magento\Authorizenet\Helper\Backend\Data::getRelayUrl()'
    ],
    [
        'getRelyUrl',
        'Magento\Authorizenet\Helper\Data',
        'Magento\Authorizenet\Helper\Data::getRelayUrl()'
    ],
    ['setPartialAuthorizationLastActionState', 'Magento\Authorizenet\Model\Authorizenet'],
    ['getPartialAuthorizationLastActionState', 'Magento\Authorizenet\Model\Authorizenet'],
    ['unsetPartialAuthorizationLastActionState', 'Magento\Authorizenet\Model\Authorizenet'],
    ['cancelPartialAuthorization', 'Magento\Authorizenet\Model\Authorizenet'],
    ['getCardsStorage', 'Magento\Authorizenet\Model\Authorizenet'],
    ['isPartialAuthorization', 'Magento\Authorizenet\Model\Authorizenet'],
    [
        'setHelper',
        'Magento\Authorizenet\Model\Directpost',
        'Magento\Authorizenet\Model\Directpost::setDataHelper()'
    ],
    [
        '_initCustomer',
        'Magento\Paypal\Controller\Adminhtml\Billing\Agreement\CustomerGrid',
        'Magento\Paypal\Controller\Adminhtml\Billing\Agreement\CustomerGrid::initCurrentCustomer'
    ],
    [
        '_initCustomer',
        'Magento\Customer\Controller\Adminhtml\Index',
        'Magento\Customer\Controller\Adminhtml\Index::initCurrentCustomer',
    ],
    ['getChilds', 'Magento\Bundle\Block\Adminhtml\Sales\Order\Items\Renderer', 'getChildren'],
    ['getChilds', 'Magento\Bundle\Block\Sales\Order\Items\Renderer', 'getChildren'],
    ['getChilds', 'Magento\Bundle\Model\Sales\Order\Pdf\Items\AbstractItems', 'getChildren'],
    ['prepareIndexdata', 'Magento\Search\Helper\Data'],
    ['isAssetMinification', 'Magento\Framework\View\Asset\ConfigInterface', 'Magento\Framework\View\Asset\Minification::isEnabled'],
    ['isAssetMinification', 'Magento\Framework\View\Asset\Config', 'Magento\Framework\View\Asset\Minification::isEnabled'],
    ['getPriceValues', 'Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable\Attribute\Collection'],
    ['getPricingValue', 'Magento\ConfigurableProduct\Model\Product\Type\Configurable\OptionValue'],
    ['getIsPercent', 'Magento\ConfigurableProduct\Model\Product\Type\Configurable\OptionValue'],
    ['addPrice', 'Magento\ConfigurableProduct\Model\Product\Type\Configurable\Attribute'],
    ['getPricingValue', 'Magento\ConfigurableProduct\Api\Data\OptionValueInterface'],
    ['setPricingValue', 'Magento\ConfigurableProduct\Api\Data\OptionValueInterface'],
    ['getIsPercent', 'Magento\ConfigurableProduct\Api\Data\OptionValueInterface'],
    ['setIsPercent', 'Magento\ConfigurableProduct\Api\Data\OptionValueInterface'],
    ['_loadPrices', 'Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable\Attribute'],
    ['getPriceValues', 'Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable\Attribute'],
    ['savePrices', 'Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable'],
    ['loadPrices', 'Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable'],
    ['_parseVariationPrices', '\Magento\ConfigurableImportExport\Model\Import\Product\Type\Configurable'],
    ['_collectSuperDataPrice', '\Magento\ConfigurableImportExport\Model\Import\Product\Type\Configurable'],
    [
        'validateForPayment',
        'Magento\Sales\Model\Order\Address\Validator',
        'Magento\Sales\Model\Order\Address\Validator::validateForCustomer'
    ],
    ['getListForCustomer', '\Magento\Quote\Api\CartItemRepositoryInterface'],
    ['saveForCustomer', '\Magento\Quote\Api\CartItemRepositoryInterface'],
    ['deleteByIdForCustomer', '\Magento\Quote\Api\CartItemRepositoryInterface'],
    ['getListForCustomer', '\Magento\Quote\Model\Quote\Item\Repository'],
    ['saveForCustomer', '\Magento\Quote\Model\Quote\Item\Repository'],
    ['deleteByIdForCustomer', '\Magento\Quote\Model\Quote\Item\Repository'],
    ['_getAdapter', 'Magento\Framework\Cache\Backend\Database', '_getConnection'],
    ['_getReadAdapter', 'Magento\Framework\DB\Helper\AbstractHelper', 'getConnection'],
    ['_getWriteAdapter', 'Magento\Framework\DB\Helper\AbstractHelper', 'getConnection'],
    ['_getConnection', 'Magento\Framework\DB\Helper\AbstractHelper', 'getConnection'],
    ['_getReadAdapter', 'Magento\Framework\Model\ResourceModel\AbstractResource', 'getConnection'],
    ['_getWriteAdapter', 'Magento\Framework\Model\ResourceModel\AbstractResource', 'getConnection'],
    ['_getReadAdapter', 'Magento\Framework\Model\ResourceModel\Db\AbstractDb', 'getConnection'],
    ['_getWriteAdapter', 'Magento\Framework\Model\ResourceModel\Db\AbstractDb', 'getConnection'],
    ['getReadConnection', 'Magento\Framework\Model\ResourceModel\Db\AbstractDb', 'getConnection'],
    ['getReadAdapter', 'Magento\Catalog\Model\Indexer\Category\Flat\AbstractAction'],
    ['getWriteAdapter', 'Magento\Catalog\Model\Indexer\Category\Flat\AbstractAction'],
    ['getReadAdapter', 'Magento\Catalog\Model\Indexer\Category\Product\AbstractAction'],
    ['getWriteAdapter', 'Magento\Catalog\Model\Indexer\Category\Product\AbstractAction'],
    ['_getWriteConnection', 'Magento\Catalog\Model\Indexer\Product\Price\Observer'],
    ['getWriteConnection', 'Magento\Catalog\Model\ResourceModel\Product\Indexer\Price\DefaultPrice'],
    ['prepareReadAdapter', 'Magento\Catalog\Model\ResourceModel\Url', 'prepareAdapter'],
    ['getReadAdapter', 'Magento\CatalogRule\Model\Indexer\IndexBuilder'],
    ['getWriteAdapter', 'Magento\CatalogRule\Model\Indexer\IndexBuilder'],
    ['getReadAdapter', 'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full'],
    ['getWriteAdapter', 'Magento\CatalogSearch\Model\Indexer\Fulltext\Action\Full'],
    ['getAdapter', 'Magento\CatalogSearch\Model\Indexer\IndexStructure'],
    ['getAdapter', 'Magento\CatalogSearch\Model\Indexer\IndexerHandler'],
    ['getReadConnection', 'Magento\CatalogSearch\Model\Search\IndexBuilder'],
    ['_getReadAdapter', 'Magento\Eav\Model\Entity\AbstractEntity'],
    ['_getWriteAdapter', 'Magento\Eav\Model\Entity\AbstractEntity'],
    ['getReadConnection', 'Magento\Eav\Model\Entity\AbstractEntity'],
    ['getWriteConnection', 'Magento\Eav\Model\Entity\AbstractEntity'],
    ['getAdapter', 'Magento\Indexer\Model\IndexStructure'],
    ['getAdapter', 'Magento\Indexer\Model\SaveHandler\IndexerHandler'],
    ['_getReadAdapter', 'Magento\MediaStorage\Model\ResourceModel\File\Storage\AbstractStorage'],
    ['_getWriteAdapter', 'Magento\MediaStorage\Model\ResourceModel\File\Storage\AbstractStorage'],
    ['getAdapter', 'Magento\Search\Model\SearchEngine'],
    ['getAdapter', 'Magento\Framework\DB\Select'],
    ['getStoreCodeFromCookie', '\Magento\Store\Model\Store'],
    ['deleteCookie', '\Magento\Store\Model\Store'],
    ['setCookie', '\Magento\Store\Model\Store'],
    ['prepareShipment', 'Magento\Sales\Model\Order'],
    ['prepareShipment', 'Magento\Sales\Model\Service\Order'],
    ['prepareInvoice', 'Magento\Sales\Model\Service\Order'],
    ['getEstimatePostCode', 'Magento\Checkout\Block\Cart\Shipping'],
    ['getEstimateCity', 'Magento\Checkout\Block\Cart\Shipping'],
    ['getEstimateRegionId', 'Magento\Checkout\Block\Cart\Shipping'],
    ['getEstimateRegion', 'Magento\Checkout\Block\Cart\Shipping'],
    ['formatPrice', 'Magento\Checkout\Block\Cart\Shipping'],
    ['isMultipleCountriesAllowed', 'Magento\Checkout\Block\Cart\Shipping'],
    ['loadProductOptions', 'Magento\Bundle\Model\Observer', 'Magento\Bundle\Observer\LoadProductOptions::invoke'],
    ['initOptionRenderer', 'Magento\Bundle\Model\Observer', 'Magento\Bundle\Observer\InitOptionRenderer::invoke'],
    ['setAttributeTabBlock', 'Magento\Bundle\Model\Observer', 'Magento\Bundle\Observer\SetAttributeTabBlock::invoke'],
    ['appendUpsellProducts', 'Magento\Bundle\Model\Observer', 'Magento\Bundle\Observer\AppendUpsellProducts::invoke'],
    ['prepareCatalogProductCollectionPrices', 'Magento\CatalogRule\Model\Observer', 'Magento\CatalogRule\Observer\PrepareCatalogProductCollectionPrices::invoke'],
    ['processAdminFinalPrice', 'Magento\CatalogRule\Model\Observer', 'Magento\CatalogRule\Observer\ProcessAdminFinalPrice::invoke'],
    ['processFrontFinalPrice', 'Magento\CatalogRule\Model\Observer', 'Magento\CatalogRule\Observer\ProcessFrontFinalPrice::invoke'],
    ['salesEventConvertQuoteAddressToOrder', 'Magento\Tax\Model\Observer'],
    ['aggregateSalesReportTaxData', 'Magento\Tax\Model\Observer', 'Magento\Tax\Observer\AggregateSalesReportTaxData::invoke'],
    ['quoteCollectTotalsBefore', 'Magento\Tax\Model\Observer', 'Magento\Tax\Observer\QuoteCollectTotalsBefore::invoke'],
    ['updateProductOptions', 'Magento\Tax\Model\Observer', 'Magento\Tax\Observer\UpdateProductOptionsObserver::invoke'],
    ['invalidateVarnish', 'Magento\CacheInvalidate\Model\Observer', 'Magento\CacheInvalidate\Observer\InvalidateVarnish::invoke'],
    ['flushAllCache', 'Magento\CacheInvalidate\Model\Observer', 'Magento\CacheInvalidate\Observer\FlushAllCache::invoke'],
    ['sendPurgeRequest', 'Magento\CacheInvalidate\Model\Observer', 'Magento\CacheInvalidate\Model\PurgeCache::sendPurgeRequest'],
    ['addCatalogToTopmenuItems', 'Magento\Catalog\Model\Observer', 'Magento\Catalog\Observer\AddCatalogToTopmenuItems::invoke'],
    ['catalogCheckIsUsingStaticUrlsAllowed', 'Magento\Catalog\Model\Observer', 'Magento\Catalog\Observer\CatalogCheckIsUsingStaticUrlsAllowed::invoke'],
    ['_addCategoriesToMenu', 'Magento\Catalog\Model\Observer', 'Magento\Catalog\Observer\AddCatalogToTopmenuItems::_addCategoriesToMenu'],
    ['hasActive', 'Magento\Catalog\Model\Observer', 'Magento\Catalog\Observer\AddCatalogToTopmenuItems::hasActive'],
    ['getCatalogLayer', 'Magento\Catalog\Model\Observer', 'Magento\Catalog\Observer\AddCatalogToTopmenuItems::getCatalogLayer'],
    ['getMenuCategoryData', 'Magento\Catalog\Model\Observer', 'Magento\Catalog\Observer\MenuCategoryData::getMenuCategoryData'],
    ['loadCustomerQuote', 'Magento\Checkout\Model\Observer', 'Magento\Checkout\Observer\LoadCustomerQuote::invoke'],
    ['salesQuoteSaveAfter', 'Magento\Checkout\Model\Observer', 'Magento\Checkout\Observer\SalesQuoteSaveAfter::invoke'],
    ['unsetAll', 'Magento\Checkout\Model\Observer', 'Magento\Checkout\Observer\UnsetAll::invoke'],
    ['currencyDisplayOptions', 'Magento\CurrencySymbol\Model\Observer', 'Magento\CurrencySymbol\Observer\CurrencyDisplayOptions::invoke'],
    ['__toString', 'Magento\Catalog\Helper\Image'],
    ['getProductImageView', 'Magento\Catalog\Block\Product\Image'],
    ['init', 'Magento\Catalog\Block\Product\Image'],
    ['getImageUrl', 'Magento\Catalog\Block\Product\View\Gallery'],
    ['getImageSize', 'Magento\Wishlist\Block\AbstractBlock'],
    ['getThumbnailSidebarUrl', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getThumbnailSidebarSize', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getSmallImageSidebarSize', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getSmallImageSidebarUrl', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getSmallImageSize', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getSmallImageUrl', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getThumbnailSize', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getThumbnailUrl', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getImageLabel', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getBaseImageUrl', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getBaseImageSize', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getBaseImageIconUrl', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getBaseImageIconSize', 'Magento\Catalog\Block\Product\AbstractProduct'],
    ['getThumbnailSidebarSize', 'Magento\Checkout\Block\Cart\Item\Renderer'],
    ['getProductThumbnailSidebarUrl', 'Magento\Checkout\Block\Cart\Item\Renderer'],
    ['getThumbnailSize', 'Magento\Checkout\Block\Cart\Item\Renderer'],
    ['getProductThumbnailUrl', 'Magento\Checkout\Block\Cart\Item\Renderer'],
    ['getThumbnailSize', 'Magento\GiftMessage\Block\Message\Inline'],
    ['getThumbnailUrl', 'Magento\GiftMessage\Block\Message\Inline'],
    ['getThumbnailSize', 'Magento\ProductAlert\Block\Email\AbstractEmail'],
    ['getThumbnailUrl', 'Magento\ProductAlert\Block\Email\AbstractEmail'],
    ['getProductThumbnail', 'Magento\Checkout\Block\Cart\Item\Renderer'],
    ['prepareInvoice', 'Magento\Sales\Api\InvoiceManagementInterface'],
    ['getColumnsComponent', 'Magento\Ui\Model\Export\ConvertToCsv', 'Magento\Ui\Model\Export\MetadataProvider::getColumnsComponent'],
    ['getColumns', 'Magento\Ui\Model\Export\ConvertToCsv', 'Magento\Ui\Model\Export\MetadataProvider::getColumns'],
    ['getHeaders', 'Magento\Ui\Model\Export\ConvertToCsv', 'Magento\Ui\Model\Export\MetadataProvider::getHeaders'],
    ['getFields', 'Magento\Ui\Model\Export\ConvertToCsv', 'Magento\Ui\Model\Export\MetadataProvider::getFields'],
    ['getRowData', 'Magento\Ui\Model\Export\ConvertToCsv', 'Magento\Ui\Model\Export\MetadataProvider::getRowData'],
    ['getComplexLabel', 'Magento\Ui\Model\Export\ConvertToCsv', 'Magento\Ui\Model\Export\MetadataProvider::getComplexLabel'],
    ['getFilterOptions', 'Magento\Ui\Model\Export\ConvertToCsv', 'Magento\Ui\Model\Export\MetadataProvider::getFilterOptions'],
    ['getOptions', 'Magento\Ui\Model\Export\ConvertToCsv', 'Magento\Ui\Model\Export\MetadataProvider::getOptions'],
    [
        'generateSimpleProducts',
        'Magento\ConfigurableProduct\Model\Product\Type\Configurable',
        'Magento\ConfigurableProduct\Model\Product\VariationHandler::generateSimpleProducts'
    ],
    [
        '_prepareAttributeSetToBeBaseForNewVariations',
        'Magento\ConfigurableProduct\Model\Product\Type\Configurable',
        'Magento\ConfigurableProduct\Model\Product\VariationHandler::prepareAttributeSetToBeBaseForNewVariations'
    ],
    [
        '_fillSimpleProductData',
        'Magento\ConfigurableProduct\Model\Product\Type\Configurable',
        'Magento\ConfigurableProduct\Model\Product\VariationHandler::fillSimpleProductData'
    ],
    ['mergeBookmarkConfig', 'Magento\Ui\Model\Manager'],
    ['addDefaultPattern', 'Magento\Theme\Model\Theme\Collection', 'addConstraint'],
    ['addTargetPattern', 'Magento\Theme\Model\Theme\Collection', 'addConstraint'],
    ['clearTargetPatterns', 'Magento\Theme\Model\Theme\Collection', 'resetConstraints'],
    ['getTargetPatterns', 'Magento\Theme\Model\Theme\Collection'],
    ['getScopeDirectory', 'Magento\Framework\View\File\Collector\Override\Base'],
    ['getScopeDirectory', 'Magento\Framework\View\File\Collector\Override\ThemeModular'],
    ['getScopeDirectory', 'Magento\Framework\View\File\Collector\Theme'],
    ['getScopeDirectory', 'Magento\Framework\View\File\Collector\ThemeModular'],
    ['_getConfigFilesList', 'Magento\Framework\App\Utility\Files'],
    ['getTitle', 'Magento\Quote\Api\Data\PaymentInterface'],
    ['setTitle', 'Magento\Quote\Api\Data\PaymentInterface'],
    ['getTitle', 'Magento\Quote\Model\Quote\Payment'],
    ['setTitle', 'Magento\Quote\Model\Quote\Payment'],
    ['getTypeSwitcherControlLabel', 'Magento\Catalog\Helper\Product'],
    ['addStockStatusToSelect', 'Magento\CatalogInventory\Helper\Stock'],
    ['getUrlInstance', 'Magento\Catalog\Model\Product\Url'],
    ['getCcOwner', 'Magento\Quote\Api\Data\PaymentInterface'],
    ['setCcOwner', 'Magento\Quote\Api\Data\PaymentInterface'],
    ['getCcNumber', 'Magento\Quote\Api\Data\PaymentInterface'],
    ['setCcNumber', 'Magento\Quote\Api\Data\PaymentInterface'],
    ['getCcType', 'Magento\Quote\Api\Data\PaymentInterface'],
    ['setCcType', 'Magento\Quote\Api\Data\PaymentInterface'],
    ['getCcExpYear', 'Magento\Quote\Api\Data\PaymentInterface'],
    ['setCcExpYear', 'Magento\Quote\Api\Data\PaymentInterface'],
    ['getCcExpMonth', 'Magento\Quote\Api\Data\PaymentInterface'],
    ['setCcExpMonth', 'Magento\Quote\Api\Data\PaymentInterface'],
    ['getShippingAddressId', 'Magento\Sales\Api\Data\OrderInterface'],
    ['getShippingMethod', 'Magento\Sales\Api\Data\OrderInterface'],
    ['getShippingAddress', 'Magento\Sales\Api\Data\OrderInterface'],
    ['setShippingAddress', 'Magento\Sales\Api\Data\OrderInterface'],
    ['getAddresses', 'Magento\Sales\Api\Data\OrderInterface'],
    ['setAddresses', 'Magento\Sales\Api\Data\OrderInterface'],
    ['setShippingAddressId', 'Magento\Sales\Api\Data\OrderInterface'],
    ['setShippingMethod', 'Magento\Sales\Api\Data\OrderInterface'],
    ['getPayments', 'Magento\Sales\Api\Data\OrderInterface'],
    ['setPayments', 'Magento\Sales\Api\Data\OrderInterface'],
    ['getQuoteAddressId', 'Magento\Sales\Api\Data\OrderAddressInterface'],
    ['setQuoteAddressId', 'Magento\Sales\Api\Data\OrderAddressInterface'],
    ['getPaymentById', 'Magento\Quote\Model\Quote'],
    ['prepareVariationCollection', 'Magento\Swatches\Helper\Data'],
    ['getProductMedia', 'Magento\Swatches\Helper\Data'],
    ['createSwatchProduct', 'Magento\Swatches\Helper\Data'],
    ['create', 'Magento\Quote\Model\QuoteRepository'],
    ['set', 'Magento\Quote\Api\GuestShippingMethodManagementInterface', 'Magento\Quote\Model\GuestCart\GuestShippingMethodManagementInterface::set'],
    ['get', 'Magento\Quote\Api\GuestShippingMethodManagementInterface', 'Magento\Quote\Model\GuestCart\GuestShippingMethodManagementInterface::get'],
    ['get', 'Magento\Quote\Api\ShippingMethodManagementInterface', 'Magento\Quote\Model\ShippingMethodManagementInterface::get'],
    ['set', 'Magento\Quote\Api\ShippingMethodManagementInterface', 'Magento\Quote\Model\ShippingMethodManagementInterface::get'],
    ['getTypeSwitcherData', 'Magento\Catalog\Block\Adminhtml\Product'],
    ['_afterLoad', 'Magento\CatalogRule\Model\ResourceModel\Rule'],
    ['_afterSave', 'Magento\CatalogRule\Model\ResourceModel\Rule'],
    ['_beforeDelete', 'Magento\Cms\Model\ResourceModel\Page'],
    ['_afterSave', 'Magento\Cms\Model\ResourceModel\Page', 'Magento\Cms\Model\ResourceModel\Page\Relation\Store\SaveHandler::execute'],
    ['_afterLoad', 'Magento\Cms\Model\ResourceModel\Page', 'Magento\Cms\Model\ResourceModel\Page\Relation\Store\ReadHandler::execute'],
    ['_beforeDelete', 'Magento\Cms\Model\ResourceModel\Block'],
    ['_afterSave', 'Magento\Cms\Model\ResourceModel\Block', 'Magento\Cms\Model\ResourceModel\Block\Relation\Store\SaveHandler::execute'],
    ['_afterLoad', 'Magento\Cms\Model\ResourceModel\Block', 'Magento\Cms\Model\ResourceModel\Block\Relation\Store\ReadHandler::execute'],
    ['getTabElement', 'Magento\Backend\Test\Block\Widget\FormTabs', 'Magento\Ui\Test\Block\Adminhtml\AbstractFormContainers::getContainerElement'],
    ['getFieldsByTabs', 'Magento\Backend\Test\Block\Widget\FormTabs', 'Magento\Ui\Test\Block\Adminhtml\AbstractFormContainers::getFixtureFieldsByContainers'],
    ['fillFormTab', 'Magento\Backend\Test\Block\Widget\Tab', 'Magento\Ui\Test\Block\Adminhtml\AbstractContainer::setFieldsData'],
    ['getDataFormTab', 'Magento\Backend\Test\Block\Widget\Tab', 'Magento\Ui\Test\Block\Adminhtml\AbstractContainer::getFieldsData'],
    ['getBunchImages', 'Magento\CatalogImportExport\Model\Import\Product'],
    ['_isAttributeValueEmpty', 'Magento\Catalog\Model\ResourceModel\AbstractResource'],
    ['var_dump', ''],
    ['each', ''],
    ['strval', ''],
    ['create_function', ''],
    ['configure', 'Magento\Framework\MessageQueue\BatchConsumer'],
    [
            'getExchangeByTopic',
            'Magento\Framework\MessageQueue\Config\Data',
            '\Magento\Framework\MessageQueue\ConfigInterface::getExchangeByTopic'
        ],
    [
            'getQueuesByTopic',
            'Magento\Framework\MessageQueue\Config\Data',
            '\Magento\Framework\MessageQueue\ConfigInterface::getQueuesByTopic'
        ],
    [
            'getConnectionByTopic',
            'Magento\Framework\MessageQueue\Config\Data',
            '\Magento\Framework\MessageQueue\ConfigInterface::getConnectionByTopic'
        ],
    [
            'getConnectionByConsumer',
            'Magento\Framework\MessageQueue\Config\Data',
            '\Magento\Framework\MessageQueue\ConfigInterface::getConnectionByConsumer'
        ],
    [
            'getMessageSchemaType',
            'Magento\Framework\MessageQueue\Config\Data',
            '\Magento\Framework\MessageQueue\ConfigInterface::getMessageSchemaType'
        ],
    [
            'getCallback',
            'Magento\Framework\MessageQueue\ConsumerConfiguration'
        ],
    [
            'getCallback',
            'Magento\Framework\MessageQueue\ConsumerConfigurationInterface'
        ],
    [
            'configure',
            'Magento\Framework\MessageQueue\ConsumerInterface'
        ],
    ['isOrderIncrementIdUsed', 'Magento\Quote\Model\ResourceModel\Quote', 'Magento\Sales\Model\OrderIncrementIdChecker::isIncrementIdUsed'],
    ['update', 'Magento\Authorization\Model\Rules', 'Magento\Authorization\Model\Rules::update'],
    ['update', 'Magento\Authorization\Model\Role', 'Magento\Authorization\Model\Role::update'],
];
