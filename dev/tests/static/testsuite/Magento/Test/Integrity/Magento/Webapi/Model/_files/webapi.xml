<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route url="/V1/customers/me" method="GET">
        <service class="Magento\Customer\Api\CustomerRepositoryInterface" method="getById" />
        <resources>
            <resource ref="Magento_Customer::customer_self" />
        </resources>
        <data>
            <parameter name="id" force="true">null</parameter>
        </data>
    </route>
    <route url="/V1/customers/me" method="PUT" secure="true">
        <service class="Magento\Customer\Api\CustomerRepositoryInterface" method="save" />
        <resources>
            <resource ref="Magento_Customer::customer_self" />
        </resources>
        <data>
            <parameter name="id">null</parameter>
        </data>
    </route>
    <route url="/V1/customers" method="POST">
        <service class="Magento\Customer\Api\CustomerRepositoryInterface" method="save" />
        <resources>
            <resource ref="Magento_Customer::manage" />
        </resources>
    </route>
    <route url="/V1/customers/:id" method="GET">
        <service class="Magento\Customer\Api\CustomerRepositoryInterface" method="getById" />
        <resources>
            <resource ref="Magento_Customer::read" />
        </resources>
    </route>
</routes>
