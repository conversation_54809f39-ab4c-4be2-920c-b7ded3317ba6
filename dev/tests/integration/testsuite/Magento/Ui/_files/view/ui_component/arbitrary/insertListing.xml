<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details. 
 */
-->
<insertListing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="config" xsi:type="array">
            <item name="autoRender" xsi:type="boolean">false</item>
            <item name="behaviourType" xsi:type="string">string</item>
            <item name="externalFilterMode" xsi:type="boolean">false</item>
            <item name="label" xsi:type="string" translate="true">string</item>
            <item name="editorProvider" xsi:type="string">string</item>
            <item name="externalCondition" xsi:type="string">string</item>
            <item name="render_url" xsi:type="url" path="anySimpleType">
                <param name="string">string</param>
            </item>
            <item name="update_url" xsi:type="url" path="anySimpleType">
                <param name="string">string</param>
            </item>
            <item name="selectionsProvider" xsi:type="string">string</item>
            <item name="externalProvider" xsi:type="string">string</item>
            <item name="loading" xsi:type="boolean">false</item>
            <item name="realTimeLink" xsi:type="boolean">false</item>
            <item name="externalData" xsi:type="string">string</item>
            <item name="dataLinks" xsi:type="array">
                <item name="imports" xsi:type="boolean">false</item>
                <item name="exports" xsi:type="boolean">false</item>
            </item>
            <item name="provider" xsi:type="string">string</item>
            <item name="component" xsi:type="string">string</item>
            <item name="template" xsi:type="string">string</item>
            <item name="sortOrder" xsi:type="number">0</item>
            <item name="displayArea" xsi:type="string">string</item>
            <item name="storageConfig" xsi:type="array">
                <item name="provider" xsi:type="string">string</item>
                <item name="namespace" xsi:type="string">string</item>
                <item name="path" xsi:type="url" path="string">
                    <param name="string">string</param>
                </item>
            </item>
            <item name="statefull" xsi:type="array">
                <item name="anySimpleType" xsi:type="boolean">true</item>
            </item>
            <item name="imports" xsi:type="array">
                <item name="string" xsi:type="string">string</item>
            </item>
            <item name="exports" xsi:type="array">
                <item name="string" xsi:type="string">string</item>
            </item>
            <item name="links" xsi:type="array">
                <item name="string" xsi:type="string">string</item>
            </item>
            <item name="listens" xsi:type="array">
                <item name="string" xsi:type="string">string</item>
            </item>
            <item name="ns" xsi:type="string">string</item>
            <item name="componentType" xsi:type="string">string</item>
            <item name="dataScope" xsi:type="string">string</item>
        </item>
        <item name="js_config" xsi:type="array">
            <item name="deps" xsi:type="array">
                <item name="0" xsi:type="string">string</item>
            </item>
        </item>
    </argument>
</insertListing>
