<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Filter\SimpleDirective\ProcessorPool">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="mydir" xsi:type="object">Magento\TestModuleSimpleTemplateDirective\Model\MyDirProcessor</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Framework\Filter\DirectiveProcessor\Filter\FilterPool">
        <arguments>
            <argument name="filters" xsi:type="array">
                <item name="foofilter" xsi:type="object">Magento\TestModuleSimpleTemplateDirective\Model\FooFilter</item>
            </argument>
        </arguments>
    </type>
</config>
